自动下载，查用户下载记录，确认是不是有这个现象，同时看用户via是不是特殊via的策略。


先用关键tag过滤出相关日志。然后查看关键字（PreUpdateAppEngine onRequest Success startDownload）是否有触发静默下载？还需要分析什么吗？


# 新版本有场景号。 8.9.5版本（4.3号加的）  2000 未知


# 预约下载
BookingAutoDownloadLongConnEngine  预约下载 长连接轮询（被动）
BookingPreDownPullEngine 预约下载 （主动） 拉配置 有预约详情 拉下载

BookingPreDown_PullEngine 新游场景的diff下载


# 关键tag

PreUpdateAppEngine
WisePreDownloadManager 
UpdateBookingLongConnEngine 
MonitorHandler call tryAutoDownload



PreUpdateAppEngine  回流静默下载
UpdateBookingLongConnEngine  预约更新

这个是数据来源的tag


执行下载都是统一的类，tag：WisePreDownloadManager



回流自动下载

# 关键字
静默下载能力关闭：
preUpdate switch is close

静默下载不允许请求：
PreUpdateAppEngine 当前已经请求过

静默下载请求到数据：
PreUpdateAppEngine onRequest Success pkg

静默下载请求的数据为空：
GetUpdatePreDownloadResponse is null sep=
PreUpdateAppEngine no data

没有触发静默下载：
tryAutoDownload PreUpdateAppEngine 没有需要更新的数据

触发了静默下载：
PreUpdateAppEngine onRequest Success startDownload


1. “PreUpdateAppEngine onRequest Success startDownload” 表示 触发了静默下载
2. “tryAutoDownload PreUpdateAppEngine 没有需要更新的数据” 表示 没有触发静默下载。
3. “preUpdate switch is close” 表示 静默下载能力关闭
4. “PreUpdateAppEngine 当前已经请求过” 表示 静默下载不允许请求。
5. “PreUpdateAppEngine onRequest Success pkg” 表示 静默下载请求到数据
6. ”GetUpdatePreDownloadResponse is null sep=” 表示 静默下载请求的数据为空
7. “PreUpdateAppEngine no data“ 表示 静默下载请求的数据为空

- “PreUpdateAppEngine onRequest Success startDownload” 表示 触发了静默下载
- “tryAutoDownload PreUpdateAppEngine 没有需要更新的数据” 表示 没有触发静默下载。
- “preUpdate switch is close” 表示 静默下载能力关闭
- “PreUpdateAppEngine 当前已经请求过” 表示 静默下载不允许请求。
- “PreUpdateAppEngine onRequest Success pkg” 表示 静默下载请求到数据
- ”GetUpdatePreDownloadResponse is null sep=” 表示 静默下载请求的数据为空
- “PreUpdateAppEngine no data“ 表示 静默下载请求的数据为空



需要关注下载的app信息（app名称，包名等）什么的吗？我看过滤出的日志里只有下载链接和包名，没有APP名称。


1. 下载的
哪些应用 外call  多少次 包名 scene 下载开始 下载删除/成功 算一次

下载最多的放前面。

- 外call下载日志  正则匹配：tmast.*&via=  过滤出来，这是via
[I][2025-04-17 +80 11:51:28.553][13610, 2*][main][market][lua_multi_game_tab_top_battle_pass_card_root.170.t032501][][key_last_uri_splash_activity_open: tmast://webview?mode=0&url=https%3A%2F%2Fovact.iwan.yyb.qq.com%2Fmoka-act%2Fc5ml78Nn4rTRD7K6muYmI1toG7%2Fpage1%2Findex.html%3Fpage%3Dindex%26ovscroll%3D0%26download_pkgnames%3Dcom.tencent.dhm1%26book_appids%3D54367612%26only_openid%3D1%26selflink%3D1%26via%3DFBI.ACT.H5.TRANSFER7262_MARKET_1_com.tencent.android.qqdownloader_5848_DEFAULT&via=FBI.ACT.H5.TRANSFER7262_MARKET_1_com.tencent.android.qqdownloader_5848_DEFAULT&start_time=1744854539014&expiry_time=1744858139014&back_jump_url=tmast%3A%2F%2Ffound



预约静默下载 次数 scene





# 通过downloadTag的 scene 字段，看看是什么场景

[YYBWebsocketServer] downloadApk :DownloadInfo{downloadState=PAUSED, appId=53987000, apkId=129515448, downloadTicket=129515448, packageName='com.xiaoe.client', name='小鹅通学员版', versionCode=905, versionName='5.12.0', scene=2007020, statScene=2007100, apkUrlList=[https://xiaoetong-1252524126.cdn.xiaoeknow.com/APP_builder_files/XiaoeApp-v5.8.5-881-xiaoe.apk], autoInstall=true, via='', taskId='', channelId='', actionFlag=0, uiType=NORMAL, fileType=APK, grayVersionCode=0, installType=2, mainUrl=null, isReplaced=false, isSwapped=false, response=DownloadResponse{length=0, totalLength=0, uiPercent=1.0, dSpeed=0.0, speed=0KB/S}, retryDownloadCnt=0, localVersionCode=0, filePath=, downloadingPath=, isUpdate=0, sllUpdate=0, isExternalCall=false, clientfilesize=0, enableTaskDualDownload=false, copyPath=null, shouldSameVersionUpdate=false}


下载场景分析。
1. 外call场景。分析应用是否为外call场景触发下载的。外call多少次。外call场景关键字如下：
    - ExternalCall
    - via=

2. 预约静默下载场景。