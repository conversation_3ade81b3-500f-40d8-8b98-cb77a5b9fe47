package com.tencent.halley.downloader;

import com.tencent.halley.NotProguard;

@NotProguard
public class DownloaderConstant {

    /**
     * 正常
     */
    public static final int OK = 0;

    /**
     * 超过最大跳转次数返回次失败码
     */
    public static final int E_OVERLOAD_MAX_REDIRECT = -1;

    /**
     * 用已知文件长度进行校验时失败
     */
    public static final int E_KNOWN_LENGTH_CHECK_FAIL = -10;

    /**
     * http返回了html页面时，返回此错误码
     */
    public static final int E_CONTENT_IS_HTML_PAGE = -11;

    /**
     * 空间不足
     */
    public static final int E_SPACE_NOT_ENOUGH = -12;

    /**
     * 创建文件时抛出IO异常（Read-only file system），sd卡只读
     */
    public static final int E_CREATE_FILE_ON_READ_ONLY_FS = -13;

    /**
     * 下载过程中保存文件时，发现文件不存在了，认为在下载过程中文件被删除，返回此错误码
     */
    public static final int E_DELETE_FILE_ON_DOWNLOADING = -14;

    /**
     * 无网络链接导致下载失败时，返回此错误码
     */
    public static final int E_NO_NETWORK = -15;

    /**
     * 下载抛出http相关异常，且系统返回网络已连接时，通过ping检查网络是否正常，不正常时返回此错误码，不计入失败
     */
    public static final int E_PING_FAIL = -16;

    /**
     * 写入下载数据时抛出IO异常(Read-only file system),sd卡只读
     */
    public static final int E_WRITE_DATA_ON_READ_ONLY_FS = -17;

    /**
     * 下载指定的路径不可用
     */
    public static final int E_SAVE_DIR_INVALID = -18;

    /**
     * 下载过程中内存不足，出现OOM导致的下载中断
     */
    public static final int E_CANCEL_ON_OOM = -19;

    /**
     * 下载自动暂停
     */
    public static final int E_CANCEL_ON_AUTO_PAUSE = -20;

    /**
     * http抛出异常类错误 start
     * 每种错误吗对应一种异常类型，未在此范围的异常统一使用错误码：E_Http_Exception
     */
    public static final int E_CLIENT_PROTOCOL_EXCEPTION = -22;

    public static final int E_CONNECT_TIMEOUT_EXCEPTION = -23;

    public static final int E_CONNECT_EXCEPTION = -24;

    public static final int E_SOCKET_TIMEOUT_EXCEPTION = -25;

    public static final int E_SOCKET_EXCEPTION = -26;

    public static final int E_IO_EXCEPTION = -27;

    public static final int E_EXCEPTION = -28;

    public static final int E_UNKNOWN_HOST_EXCEPTION = -29;

    public static final int E_HTTP_HOST_CONNECT_EXCEPTION = -30;
    // http抛出异常类错误 end

    // https握手相关
    public static final int E_SSL_HAND_SHAKE_EXCEPTION = -31;
    public static final int E_SSL_HAND_KEY_EXCEPTION = -32;
    public static final int E_SSL_PEER_UNVERIFIED_EXCEPTION = -33;
    public static final int E_SSL_PROTOCOL_EXCEPTION = -34;
    public static final int E_SSL_EXCEPTION = -35;
    public static final int E_GENERAL_SECURITY_EXCEPTION = -36;
    public static final int E_INTERRUPTED_IO_EXCEPTION = -37;
    public static final int E_CERT_PATH_VALIDATOR_EXCEPTION = -38;

    /**
     * 保存文件到手机存储空间且手机存储空间不足时，返回此错误码
     */
    public static final int E_PHONE_SPACE_FULL = -40;

    /**
     * url探测失败，http请求返回200，但是返回头中未携带Content-Range或者Content-Length字段，无法获得文件大小，则返回此错误码
     */
    public static final int E_DETECT_FAIL = -41;

    /**
     * 分段下载过程中，下载到的分段数据与当前的分段状态不符合时，返回此错误码，主要用来校验分段下载数据顺序，一般不会发生
     */
    public static final int E_RANGE_CHECK_FAIL = -42;

    /**
     * 断点续传时校验每次http请求返回的文件总长度与之前探测到的文件总长度是否一致，不一致认为文件发生变化，返回此错误码
     */
    public static final int E_TOTAL_LEN_CHECK_FAIL = -43;

    /**
     * 断点续传时校验每次http请求返回的etag信息与之前探测到的etag信息是否一致，不一致认为文件发生变化，返回此错误码
     */
    public static final int E_ETAG_CHECK_FAIL = -44;

    /**
     * 探测到文件长度，在存储空间足够的情况下，设置文件长度时抛出异常，返回此错误码，一般不会发生
     */
    public static final int E_SET_FILE_LEN_FAIL = -45;

    /**
     * 保存到SD卡但是SD卡不可用时，返回此错误码
     */
    public static final int E_SD_CARD_NOT_READY = -46;

    /**
     * 保存的路径不存在时，既不是SD卡，也不是手机存储空间，返回此错误码
     */
    public static final int E_SAVE_DIR_NOT_EXISTS = -47;

    /**
     * http请求及数据下载过程中抛出异常导致下载失败，返回此错误码，app可通过getFailInfo获取异常信息描述，格式为：异常类型名称|异常堆栈信息
     */
    public static final int E_HTTP_EXCEPTION = -48;

    /**
     * 根据保存文件的路径创建新文件以及创建随机写入文件对象时抛出异常，则返回此错误码，算入失败率
     */
    public static final int E_CREATE_FILE_ERROR = -49;

    /**
     * 写入下载数据时抛出异常导致下载失败，且无法排除是否因为系统本身原因导致的，则返回此错误码，算入失败率
     */
    public static final int E_WRITE_DATA_ERROR = -50;

    /**
     * url无效
     */
    public static final int E_URL_INVALID = -51;

    /**
     * 打电话导致下载失败
     */
    public static final int E_PHONE_CALL_MODE = -52;

    /**
     * 不支持分段，一般会尝试不分段的下载请求，所以后台一般不会收到此错误码上报
     */
    public static final int E_NOT_SUPPORT_RANGE = -53;

    /**
     * 根据content-range头解析文件长度失败
     */
    public static final int E_PARSE_CONTENT_RANGE_FAIL = -54;

    /**
     * 响应中无content-length头
     */
    public static final int E_NO_CONTENT_LENGTH_HEADER = -55;

    /**
     * 根据content-length头解析文件长度失败
     */
    public static final int E_PARSE_CONTENT_LENGTH_FAIL = -56;

    /**
     * url跳转，一般不会上报到后台，跳转太多时上报E_Overload_Max_Redirect
     */
    public static final int E_NORMAL_REDIRECT = -57;

    /**
     * 请求发生跳转，但无法获取新的location信息
     */
    public static final int E_NO_LOCATION_ON_REDIRECT = -58;

    /**
     * wap网关进行了长度限制返回码，在此时需要进行限制range长度后发起请求
     */
    public static final int E_WAP_LENGTH_LIMITED = -59;

    /**
     * 文件特征码校验失败
     */
    public static final int E_FEATURE_VERIFY_FAIL = -60;

    /**
     * 客户端实现错误导致下载失败的情况
     */
    public static final int E_CLIENT_FAIL = -61;

    /**
     * 读取数据未完成，读取某个范围的数据段时，只读取了一部分，便结束了，read返回-1
     */
    public static final int E_READ_UNFINISH = -62;

    /**
     * FeatureReq特征码获取请求的响应头中Content-Range包含的文件总长度与已知长度不一致
     */
    public static final int E_FEATURE_REQ_FILE_LENGTH_CHECK_FAIL = -63;

    /**
     * 分段数据读取请求的响应头中Content-Range包含的文件总长度与已知长度不一致
     */
    public static final int E_SECTION_DATA_REQ_FILE_LENGTH_CHECK_FAIL = -64;

    /**
     * 分段数据请求范围超出了服务端资源总长度
     */
    public static final int E_RANGE_NOT_SATISFIABLE = -65;

    /**
     * 网络请求被丢弃（内部错误码，一般用于暂停或者其他错误导致的强制中断）
     */
    public static final int E_REQUEST_ABORT = -66;

    /**
     * 启动“直下线程”任务失败，属于内部逻辑控制错误导致的异常，正常情况不会出现
     */
    public static final int E_EXCUTE_DIRECT_TRANSPORT_FAIL = -67;

    /**
     * 断点续传时校验每次http请求返回的lastmodified字段，不一致则出现此错误码
     */
    public static final int E_VERIFY_LAST_MODIFIED_FAIL = -68;

    /**
     * 下载线程都执行完成，但是最后执行完成的下载线程错误码为OK，但是实际任务并未下载完成，一般属于内部逻辑错误
     */
    public static final int E_INNER_FAIL = -69;

    /**
     * http请求过程中抛出的非Exception的Throwable，上报此错误码，与-48有所区分
     */
    public static final int E_HTTP_THROWABLE = -70;

    /**
     * http请求过程中抛出的异常，提示缺失网络权限，上报此错误码，与某些定制系统对网络权限的特殊限制有关
     */
    public static final int E_MISS_PERMISSION = -71;

    /**
     * 重命名失败
     */
    public static final int E_RENAME_FAIL = -72;

    /**
     * 请求返回的文件size与调度得到文件size不一致
     */
    public static final int E_SCHEDULE_SIZE_CHECK_FAIL = -73;

    /**
     * 不允许跳转时发生跳转行为
     */
    public static final int E_JUMP_DESPITE_BAN = -74;

    /**
     * 强制校验所有校验任务都失败
     */
    public static final int E_FORCE_VERIRY_ALL_FAILED = -75;

    /**
     * https的content-length与调度后台的值不一致
     */
    public static final int E_HTTPS_SIZE_NOT_EQUAL = -76;

    /**
     * Doze模式
     */
    public static final int E_DOZE_MODE = -77;

    /**
     * 没有文件写入权限
     */
    public static final int E_PERMISSION_DENIED = -78;

    /**
     * pcdn未开启
     */
    public static final int E_PCDN_DISABLE = -80;

    /**
     * pcdn请求失败
     */
    public static final int E_PCDN_REQ_FAIL = -81;

    /**
     * pcdn响应失败
     */
    public static final int E_PCDN_RSP_FAIL = -82;

    /**
     * pcdn数据写入失败
     */
    public static final int E_PCDN_WRITE_DATA_FAIL = -83;
}

