1. 根据网络请求，查看是否有error。tag：HttpRequest   tag：HttpProtocolInterceptor
请求根代码：com.tencent.assistantkuikly.core.http.PageRequestEngine#sendRequest
jce请求代码：com.tencent.assistantkuikly.core.request.HttpProtocolInterceptor#intercept
http请求代码：com.tencent.assistantkuikly.core.http.HttpRequest#doSendHttpRequest

PageReporter_beaconReport 的上报 erro_code


2. 如果有
   - 是不是后台下发的数据问题
   - 是不是不满足条件
   - 是不是请求到测试环境


# 错误码查看方式
错误码格式化代码：com/tencent/assistantkuikly/component/moka_data_core/domain/user_cases/response_adapter.kt:84

[I][2025-04-17 +80 12:07:28.352][32187, 8852][kuikly_thread-8][market][HttpRequest][][|12:07.28.314|[KLog][HttpRequest]:doSendHttpRequest message url: https://m.yyb.qq.com/access/v3/comeback_query_qq, body: {}, response: {"statusCode": 0,"code": 200,"headers": {"date": "Thu, 17 Apr 2025 04:07:28 GMT","content-type": "application\/json","access-control-allow-credentials": "true","access-control-allow-headers": "x-requested-with,content-type","access-control-allow-methods": "POST,GET,OPTIONS","access-control-allow-origin": "https:\/\/ovact.iwan.yyb.qq.com","trpc-trans-info": "{\"affinity_attribute\":\"dHJwYy5odHRwLnVwc2VydmVyLnVwc2VydmljZeKUglBDRy0xMjMuWVlCT1AuVW5pZmllZEFjY2Vzc0xheWVy4pSCMg==\"}","ual-access-nonce": "1253997649","ual-access-pbinfo": "61417:1.2.9","ual-access-requestid": "mole-1744862848047-yyb_h5","ual-access-ret": "0","ual-access-signature": "3bca2a6807e9cf78c4bc3cba2745cb73","ual-access-timestamp": "1744862848","x-content-type-options": "nosniff"},"body": "{\"code\":300033,\"err_msg\":\"redis数据查询为空\",\"qq\":\"\"}","message": "","requestTime": 1744862848128,"responseTime": 1744862848300}







- 请求到测试环境 -- 403 Forbidden

[KLog][HttpRequest]:doSendHttpRequest message url: https://m-test.yyb.qq.com/access/v3/activate_and_auth_for_h5, header: {activity_iid=aiid_8e78673c-08ba-444d-b23b-e16f392adc73, authority=m-test.yyb.qq.com, origin=https://ovact.iwan.yyb.qq.com, referer=https://ovact.iwan.yyb.qq.com/moka-act/j2TU8mCQ2dU0ublhlLCGEGN0s5/page1/index.html?page=index&ovscroll=0&download_pkgnames=com.tencent.dhm1&book_appids=54367612&only_openid=1&kuiklyVersion=23&kuiklyPageName=Nativej2TU8mCQ2dU0ublhlLCGEGN0s5&activityKey=j2TU8mCQ2dU0ublhlLCGEGN0s5, env=prod, content-type=application/json; charset=UTF-8, ual-access-requestid=moka-1744864984197, ual-access-businessid=yyb_h5, ual-access-timestamp=1744864984197, ual-access-nonce=84, ual-access-signature=e9e62ebce215509a5b1b8d43f5b3bba2, ual-access-login-appid=1101070898, ual-access-login-type=1, ual-access-openid=3210A3285F2FCC7E7D329968B13EAF9C, ual-access-access-token=08385BF49A2688DBE693163C786C446B, cookie=encrypt_q36id=; openid=; accesstoken=; skey=; skey_datetime=; uin=; sid=; vkey=; guid=2031623384944894400; via=; isforeground=1; qid=8488aa694312298ee6e4520b100017e17911; q36id=8488aa694312298ee6e4520b100017e17911; qopenid=null; qaccesstoken=null; openappid=0; sdkVersion=35; abiList=["arm64-v8a","armeabi-v7a","armeabi"]; logintype=MOBILEQ; mobileqopenid=3210A3285F2FCC7E7D329968B13EAF9C; mobileqaccesstoken=08385BF49A2688DBE693163C786C446B; mobileqpaytoken=88BE5F86EBCCA19B123DE37884A1032D; caller=1}, body: {"app_id": 54367612,"scene_id": "230017","is_all_app": false,"is_only_query": true,"wait_time": 1000}, response: {"statusCode": 0,"code": 403,"headers": {"server": "stgw","date": "Thu, 17 Apr 2025 04:43:03 GMT","content-type": "text\/html","content-length": "547","alt-svc": "h3=\":443\"; ma=2592000, h3-29=\":443\"; ma=2592000, h3-27=\":443\"; ma=2592000, h3-Q050=\":443\"; ma=2592000, h3-Q046=\":443\"; ma=2592000, h3-Q043=\":443\"; ma=2592000, h3-Q039=\":443\"; ma=2592000, quic=\":443\"; ma=2592000; v=\"39,43,46\"","access-control-expose-headers": "trpc-func-ret, trpc-error-msg, ual-access-ret"},"body": "<html>\r\n<head><title>403 Forbidden<\/title><\/head>\r\n<body>\r\n<center><h1>403 Forbidden<\/h1><\/center>\r\n<hr><center>stgw<\/center>\r\n<\/body>\r\n<\/html>\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n","message": "","requestTime": 1744864984447,"responseTime": 1744864984515}





- PageReporter_beaconReport 的上报 erro_code=-99999

[I][2025-04-17 +80 12:45:21.521][28962, 33757][kuikly_thread-6][market][PageReporter_beaconReport][][|12:45.21.521|[KLog][PageReporter_beaconReport]:reportActivityComponentResult Params: {component_name=会员激活授权组件, component_id=vip-activate-auth_b44c08e1, activity_id=j2TU8mCQ2dU0ublhlLCGEGN0s5, page_name=Nativej2TU8mCQ2dU0ublhlLCGEGN0s5, btn_name=会员激活授权组件, erro_code=-99999, erro_msg=请求异常, sourceid=, resourceid=, ptag=, open_id=o4f6JuHaNtGMiubFItHJH3sAbFmE, act_id=126964}