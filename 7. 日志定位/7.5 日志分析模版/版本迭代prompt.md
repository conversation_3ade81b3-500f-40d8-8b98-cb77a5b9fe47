好的，我帮你完善 `SceneData` 类，增加一些常见方法，方便版本管理和数据操作。这里用 `dataclass`，并且把常用功能封装成类方法和实例方法，比如：

- 转 dict / JSON
- 更新内容（自动版本+1）
- 比较版本
- 简单的验证
- 打印展示

另外，提供一个管理类 `SceneManager`，用于管理多条 `SceneData`，支持：

- 添加新版本
- 获取最新版本
- 获取所有版本
- 保存到文件 / 从文件加载

---

## 完整示例代码

```python
import json
from dataclasses import dataclass, asdict, field
from typing import Optional, List, Dict, Any


@dataclass
class SceneData:
    scene_id: str
    version: int = 1
    tag: Optional[List[str]] = field(default_factory=list)
    prompt: Optional[str] = None
    delete_content: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转成字典"""
        return asdict(self)

    def to_json(self, ensure_ascii=False) -> str:
        """转成 JSON 字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=ensure_ascii)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SceneData":
        """从字典创建实例"""
        return cls(**data)

    @classmethod
    def from_json(cls, json_str: str) -> "SceneData":
        """从 JSON 字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)

    def update(self, tag: Optional[List[str]] = None, prompt: Optional[str] = None,
               delete_content: Optional[str] = None) -> "SceneData":
        """
        生成一个新版本的 SceneData，版本号自动+1，内容更新
        返回新的 SceneData 实例，不修改当前实例
        """
        new_version = self.version + 1
        return SceneData(
            scene_id=self.scene_id,
            version=new_version,
            tag=tag if tag is not None else self.tag,
            prompt=prompt if prompt is not None else self.prompt,
            delete_content=delete_content if delete_content is not None else self.delete_content,
        )

    def __str__(self):
        return (f"SceneData(scene_id={self.scene_id}, version={self.version}, "
                f"tag={self.tag}, prompt={self.prompt}, delete_content={self.delete_content})")

    def validate(self) -> bool:
        """
        简单验证，比如 scene_id 不为空，version >=1
        你可以根据需求扩展
        """
        if not self.scene_id:
            raise ValueError("scene_id不能为空")
        if self.version < 1:
            raise ValueError("version必须>=1")
        return True


class SceneManager:
    """
    管理多个 SceneData，支持版本管理和文件存储
    """

    def __init__(self):
        # key: scene_id, value: list of SceneData，按版本升序排列
        self._data: Dict[str, List[SceneData]] = {}

    def add_scene(self, scene: SceneData):
        """添加一条数据，自动按版本排序"""
        scene.validate()
        versions = self._data.setdefault(scene.scene_id, [])
        # 检查版本号是否重复
        if any(s.version == scene.version for s in versions):
            raise ValueError(f"scene_id={scene.scene_id} 的版本 {scene.version} 已存在")
        versions.append(scene)
        versions.sort(key=lambda s: s.version)

    def get_latest(self, scene_id: str) -> Optional[SceneData]:
        """获取指定 scene_id 的最新版本"""
        versions = self._data.get(scene_id)
        if not versions:
            return None
        return versions[-1]

    def get_all_versions(self, scene_id: str) -> List[SceneData]:
        """获取指定 scene_id 的所有版本，按版本升序"""
        return self._data.get(scene_id, [])

    def update_scene(self, scene_id: str, tag: Optional[List[str]] = None,
                     prompt: Optional[str] = None, delete_content: Optional[str] = None) -> SceneData:
        """
        基于最新版本更新，生成新版本并添加
        返回新版本 SceneData
        """
        latest = self.get_latest(scene_id)
        if latest is None:
            # 如果不存在，创建版本1
            new_scene = SceneData(scene_id=scene_id, version=1, tag=tag, prompt=prompt, delete_content=delete_content)
        else:
            new_scene = latest.update(tag=tag, prompt=prompt, delete_content=delete_content)
        self.add_scene(new_scene)
        return new_scene

    def to_file(self, filename: str):
        """保存所有数据到文件，每条一行 JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            for versions in self._data.values():
                for scene in versions:
                    f.write(scene.to_json(ensure_ascii=False) + '\n')

    def load_file(self, filename: str):
        """从文件加载数据，追加到当前管理器"""
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                scene = SceneData.from_json(line)
                try:
                    self.add_scene(scene)
                except ValueError as e:
                    print(f"跳过重复版本数据: {e}")

    def __str__(self):
        lines = []
        for scene_id, versions in self._data.items():
            lines.append(f"scene_id={scene_id}:")
            for scene in versions:
                lines.append(f"  {scene}")
        return "\n".join(lines)


# ====== 示例用法 ======

if __name__ == "__main__":
    manager = SceneManager()

    # 新增版本1
    s1 = SceneData(scene_id="scene_001", version=1, tag=["tag1"], prompt="提示v1", delete_content="删除v1")
    manager.add_scene(s1)

    # 基于最新版本更新，生成版本2
    s2 = manager.update_scene("scene_001", tag=["tag1", "tag2"], prompt="提示v2")

    # 新增另一条数据
    s3 = SceneData(scene_id="scene_002", version=1, tag=["tagA"], prompt="提示A", delete_content=None)
    manager.add_scene(s3)

    print("所有数据:")
    print(manager)

    # 保存到文件
    filename = "scenes_data.txt"
    manager.to_file(filename)

    # 新建管理器，加载文件
    new_manager = SceneManager()
    new_manager.load_file(filename)
    print("\n从文件加载的数据:")
    print(new_manager)
```

---

## 说明

- `SceneData` 类封装了数据和常用操作，`update` 方法生成新版本实例。
- `SceneManager` 管理多条数据，支持版本管理、文件存储和加载。
- 文件格式是每条数据一行 JSON，方便追加和读取。
- 你可以根据需求扩展验证、查询、删除等功能。

如果你需要，我可以帮你写更复杂的查询接口或数据库版本管理示例。