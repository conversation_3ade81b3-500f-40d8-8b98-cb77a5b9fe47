# 接收信息
1. 知识库、日志分析流程（优质的需要保存）
2. 需要过滤的tag、保存的tag（过滤后的日志展示？）


创建新场景，重复利用？

Aisee分析，人工选择场景 -> 代码进入对应场景 -> 场景代码
                                        newAgent.py
                                        newPrompt.py
设计通用的 newAgent.py 传入参数




fetch_log_client 只 拉取日志文件，获得日志文件夹路径。
日志过滤作为一个类 - 选择过滤的文件









如果要重复利用，用户自己选择保存

放入字典
场景  - tag、Prompt

- 模板例子
- 怎么进行prompt版本迭代，如果有增量的话，选取哪个prompt进行分析？ 
- 怎么判断哪个版本的场景更好？




# 待开发

## is_analyze_daemon: bool = False # 是否分析 daemon 进程
## 日志行提取json？



