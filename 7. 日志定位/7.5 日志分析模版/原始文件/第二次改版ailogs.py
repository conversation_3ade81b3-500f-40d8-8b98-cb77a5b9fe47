import os

from AiLogs.logs.logger import app_logger
from AiLogs.client.fetch_log_client import FetchLogClient
from AiLogs.agent import UserIntent
from AiLogs.agent.filter_user_action import FilterUserAction
from AiLogs.agent.base_agent import BaseAgent
from datetime import datetime, timedelta
from AiLogs.tools import log_process as lp

class AiLogs:
    def __init__(self):
        pass
    
    def analyze_log(self, query, config=None):
        if config is None:
            config = {}
        log_save_path = config.get('log_save_path', '')
        bug_time = config.get('bug_time', '')
        log_download_link = config.get('log_download_link', '')
        log_path = config.get('log_path', '')
        is_check_all_logs = config.get('is_check_all_logs', False)
        log_tags = config.get('log_tags', [])
        prompt = config.get('prompt', '')
        index_keys = config.get('index_keys', {})
        save_logs = config.get('save_logs', [])
        delete_logs = config.get('delete_logs', [])
        split_info = config.get('split_info', [])
        dedup_targets = config.get('dedup_targets', [])
        extract_fields = config.get('extract_fields', {})
        is_fuzzy_match_tag = config.get('is_fuzzy_match_tag', False)
        is_analyze_daemon = config.get('is_analyze_daemon', False)
        
        
        if log_download_link == "" and log_path == "":
            raise ValueError("log_download_link 和 log_path 不能同时为空")

        # 日志文件夹路径
        logs_path = ''
        # 如果没有传入log_path，根据 log_download_link 解压
        if log_path == "":
            # 解析下载链接 获取日志文件路径
            fetch_log = FetchLogClient(download_link=log_download_link, 
                                        bug_time=bug_time,
                                        query=query, 
                                        log_save_path=log_save_path, 
                                        is_analyze_daemon=True)
            logs_path = fetch_log.fetch_log_from_url()
        else:
            # 检查是不是压缩包
            is_zip_file = self._is_zip_file(log_path)
            if is_zip_file:
                # 是压缩包，解压并返回对应的日志文件夹路径
                fetch_log = FetchLogClient(download_link=log_path, 
                                        bug_time=bug_time,
                                        query=query, 
                                        log_save_path=log_save_path, 
                                        is_analyze_daemon=True)
                logs_path = fetch_log.unzip_and_fetch_log(log_path)

        app_logger.info(f'日志文件夹 路径： = {logs_path}')
        if not logs_path:
            app_logger.info("未找到日志文件夹")
            raise ValueError("未找到日志文件夹")

        start_time = None
        end_time = None
        # 开始分析日志
        print("AI 开始分析 您的日志... ")
        userActionAgent = FilterUserAction(
                    query=query,
                    logs_path=logs_path,
                    log_tags=log_tags,
                    prompt=prompt,
                    bug_time=bug_time,
                    index_keys=index_keys,
                    save_logs=save_logs,
                    delete_logs=delete_logs,
                    split_info=split_info,
                    dedup_targets=dedup_targets,
                    extract_fields=extract_fields,
                    is_fuzzy_match_tag=is_fuzzy_match_tag,
                    is_analyze_daemon=is_analyze_daemon)

        for result in userActionAgent.parse_user_action():
            yield {"data": result['data'], "type": result['type']}

    def _is_zip_file(self, file_path: str) -> bool:
        """检查是否为ZIP文件（根据文件头）"""
        try:
            with open(file_path, "rb") as f:
                return f.read(4) in (b"PK\x03\x04", b"PK\x05\x06", b"PK\x07\x08")
        except Exception:
            return False


    def save_result_to_file(self, result, query):
        os.makedirs('.test_AI/', exist_ok=True)
        save_path = os.path.join('.test_AI/', f'{query}.md')
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(result)

if __name__ == "__main__":
    # log_download_link = 'https://cms.myapp.com/xy/yybtech/74q9dxpn.zip'
    log_download_link = ""
    # log_path = "/Users/<USER>/yyb/logasistent/data/com.tencent.android.qqdownloader_2025040312.xlog.log"
    log_path =  "/Users/<USER>/Desktop/日志分析/activity - 用户问题不准确，aisee协助将分类输入到问题中/aisee/1. 领取-发货失败/1.我群星纪元首发活动，5w站力值领的2qb发送失败了，幸幸苦苦刚打完，发送失败怎么回事！要求人工重发！！！/TDOSLog_20250419_000553462_21752_58453_decrypted.zip"
    # query = '安装失败'
    # query = '参加活动，条件不满足1'
    # query = '发货失败'
    query = '视频'
    log_save_path='data_test1/log_1'
    # bug_time='2025-03-20 09:33:00'
    bug_time = '2025-04-18 23:49:25.000'
    log_tags = ['PageReporter_beaconReport', 'HttpProtocolInterceptor', 'HttpRequest',
                          'HTTPRequester', 'RuntimeCrash',
                          'ReceivingRewardViewModel', 'YybActCommonReceiveManager',
                          'YybLotteryView','YybLotteryViewModel']
    log_analyzer = AiLogs()
    for result in log_analyzer.analyze_log(log_download_link=log_download_link, query=query, log_save_path=log_save_path, bug_time=bug_time, log_path=log_path, log_tags=log_tags):
        if result['type'] == 'result_answer':
            # app_logger.info(f"AiLogs最终结果：{result['data']}")
            log_analyzer.save_result_to_file(result['data'], query)
            # print(f"数据: {result['data']}, 类型: {result['type']}")

