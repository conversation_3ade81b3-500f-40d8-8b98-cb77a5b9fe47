import os
import re
from datetime import datetime

class BaseAgent():
    def __init__(self, logPath, startTime, endTime, query, user_action, logs_path, log_tags):
        self._log_path = logPath
        self._start_time = startTime
        self._end_time = endTime
        self._query = query
        self._user_action = user_action
        self._logs_path = logs_path
        self._log_tags = log_tags

    def parse_kuikly_activity_log(self):
        app_logger.info('======= 开始 场景 分析 =========')

        # 定义需要提取的字段和对应LogProcessor方法名
        extract_fields = {
            "lottery_item_info": "format_log_to_lottery_item_info",
            "obtain_present_info": "format_log_to_obtain_present_info",
            "click_info": "format_log_to_click_info"
        }

        filteredLogs, extracted_info = self._filtered_log(self._log_path, extract_fields)

        # 如果过滤的日志为空(行数太少就继续合并)
        if not filteredLogs:
            filteredLogs, extracted_info = self._filtered_log_again(extract_fields)

        # 你也可以选择调用 _filtered_all_log()，这里示例注释掉
        # filteredLogs, extracted_info = self._filtered_all_log(extract_fields)

        prompt = KUIKLY_ACTIVITY_PROMPT.format(
            rag="",
            log_content="".join(filteredLogs),
            query=self._query,
            lottery_item_info=extracted_info.get("lottery_item_info", ""),
            user_action=self._user_action,
            obtain_present_info=extracted_info.get("obtain_present_info", ""),
            click_info=extracted_info.get("click_info", "")
        )
        yield from self._request_model(prompt)

    def _filtered_log(self, log_path, extract_fields=None):
        log_processor = LogProcessor()
        log_processor.process(log_path)
        filteredLogs = log_processor.filter_log_by_tags_and_time(self._start_time, self._end_time, self._log_tags)

        # 默认字段配置
        if extract_fields is None:
            extract_fields = {
                "lottery_item_info": "format_log_to_lottery_item_info",
                "obtain_present_info": "format_log_to_obtain_present_info",
                "click_info": "format_log_to_click_info"
            }

        extracted_info = {}
        for key, method_name in extract_fields.items():
            method = getattr(log_processor, method_name, None)
            if callable(method):
                extracted_info[key] = method(filteredLogs)
                app_logger.info(f"{key} = {extracted_info[key]}")
            else:
                extracted_info[key] = None
                app_logger.warning(f"LogProcessor 没有方法 {method_name}，{key} 设置为 None")

        # 保存的关键日志，连续重复的日志，只保存第一条。
        save_logs = [
            ('ReceivingRewardViewModel', 'orderStatus=3'),
            ('YybActCommonReceiveManager', 'doShowResult instance'),
            ('PageReporter_beaconReport', 'reportActivityComponentClick'),
            ('YybLotteryView', ''),
            ('YybLotteryViewModel', '')
        ]
        # 将日志行中无用的部分截除
        split_info = [
            ('ReceivingRewardViewModel', 'img'),
            ('YybActCommonReceiveManager', 'propertyData')
        ]
        dedup_targets = [
            ('ReceivingRewardViewModel', 'doQueryLotteryResult item')
        ]
        filteredLogs = log_processor.save_log_by_tags_and_content(
            filteredLogs,
            save_logs,
            split_info=split_info,
            is_fuzzy_match_tag=True,
            dedup_targets=dedup_targets
        )

        # 堆栈信息过滤
        filteredLogs = log_processor.remove_at_com_content(filteredLogs)

        return filteredLogs, extracted_info

    def _filtered_log_again(self, extract_fields=None):
        """如果最新的两个日志文件过滤出的日志内容为空，将日志文件新到旧排序，继续过滤。"""
        logs = []
        filteredLogs = []
        extracted_info = {}
        file_in_path = os.listdir(self._logs_path)
        for file_name in file_in_path:
            if '@' not in file_name:
                logs.append(file_name)
        logs = sorted(logs, key=self._extract_key)
        app_logger.info(f'filtered_log_again === logs: {logs}')
        if len(logs) > 1:
            # 从第2个日志文件开始
            for log_name in logs[1:]:
                filteredLogs, extracted_info = self._filtered_log(os.path.join(self._logs_path, log_name), extract_fields)
                if filteredLogs:
                    app_logger.info(f'filtered_log_again === log_name: {log_name}')
                    break
        return filteredLogs, extracted_info

    def _filtered_all_log(self, extract_fields=None):
        """对所有主进程日志文件排序日期，合并，进行过滤分析"""
        logs = []
        file_in_path = os.listdir(self._logs_path)
        for file_name in file_in_path:
            if '@' not in file_name:
                logs.append(file_name)
        logs = sorted(logs, key=self._extract_key)
        app_logger.info(f'filtered_all_log === logs: {logs}')
        log_files = []
        if len(logs) > 1:
            for i, log_name in enumerate(logs):
                if i == 4:  # 到第5个日志文件时退出循环
                    break
                log_files.append(os.path.join(self._logs_path, log_name))
            merged_log_path = self._merge_logs(log_files)
            return self._filtered_log(merged_log_path, extract_fields)
        else:
            return [], {}

    def _extract_key(self, log):
        """
        获取日志文件名的时间戳，以便排序。
        支持格式示例：
        - xxx_2025042009_1.xlog.log
        - xxx_2025042007.xlog.log
        """
        pattern = re.compile(r'_(\d{10})(?:_(\d+))?\.xlog\.log$')
        match = pattern.search(log)
        if not match:
            # 匹配失败，返回最小值，保证排序时放在最后
            return ('0000000000', 0)
        timestamp = match.group(1)
        suffix_str = match.group(2)
        try:
            suffix = int(suffix_str) if suffix_str else 0
        except ValueError:
            suffix = 0
        return (timestamp, suffix)

    def _merge_logs(self, log_files):
        """合并日志文件"""
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        merged_log_path = os.path.join(self._logs_path, f'merged_log_{current_time}.log')
        with open(merged_log_path, 'wb') as merged_file:
            for log_file in log_files:
                app_logger.info(f'正在合并 {log_file}')
                with open(log_file, 'rb') as f:
                    merged_file.write(f.read())
                    merged_file.write(b'\n')  # 添加换行符以分隔日志
        app_logger.info(f"合并日志文件成功，保存到: {merged_log_path}")
        return merged_log_path

    def _request_model(self, prompt):
        app_logger.info(prompt)

        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "Hunyuan-T1-32K"
        # model = "DeepSeek-R1"
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        yield from self._stream_results(hunyuan_client.request(prompt))

    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}