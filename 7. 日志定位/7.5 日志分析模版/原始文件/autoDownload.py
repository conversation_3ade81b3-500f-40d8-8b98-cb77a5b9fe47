from AiLogs.rag.log_rag import log_rag
from AiLogs.tools.log_process import LogProcessor
from AiLogs.logs.logger import app_logger
from AiLogs.prompt.auto_download_prompt import AUTO_DOWNLOAD_PROMPT
from AiLogs.client.hunyuan_client import HunyuanClient


class AutoDownloadAgent():
    def __init__(self, logPath, startTime, endTime, userAction, query):
        self._log_path = logPath
        self._start_time = startTime
        self._end_time = endTime
        self._user_action = userAction
        self._query = '请分析是否触发了自动下载，在什么场景触发自动下载？'
        self._auto_download_tags = ['PreUpdateAppEngine', 'WisePreDownloadManager',
                               'UpdateBookingLongConnEngine','MonitorHandler call tryAutoDownload',
                               'BookingPreDown_PullEngine',
                               'ExternalCall', 'DownloadTag']
        self._indexKeys = {'scene', 'statScene'}

    def parse_auto_download_log(self):
        app_logger.info('======= 开始 自动下载 分析  =========')

        log_processor = LogProcessor()
        log_processor.process(self._log_path)
        filteredLogs = log_processor.filter_log_by_tags_and_time(self._start_time, self._end_time,
                                                                 self._auto_download_tags)

        # RAG 补充相关知识， 构建索引
        indexValues = set()
        indexKeys = set()
        log_rag.prepareLogRag()
        for logLine in filteredLogs:
            for indexKey in self._indexKeys:
                key = log_processor.findIndexKeyFromLog(indexKey, logLine)
                if key:
                    indexKeys.add(key)
                    key = key.replace(" ", "")
                    key = key.replace("=", ":")
                    indexValues.add(log_rag.queryExplainByCode(key))
        rag = ""
        for indexValue in indexValues:
            rag += f"{indexValue}\n"
        app_logger.info("".join(rag))
        # app_logger.info(f'===== 首次过滤下载日志 ======\n{"".join(filteredLogs)}')

        # 保存的关键日志，连续重复的日志，只保存第一条。
        save_logs = [('DownloadTag', ''), 
                     ('PreUpdateAppEngine', ''),
                     ('WisePreDownloadManager', ''), 
                     ('UpdateBookingLongConnEngine', ''), 
                     ('MonitorHandler call tryAutoDownload', ''), 
                     ('BookingPreDown_PullEngine', ''),
                     ('ExternalCall', '&via=')]
        filteredLogs = log_processor.save_log_by_tags_and_content(filteredLogs, save_logs)

        # 无用日志行
        delete_logs = [('DownloadTag', 'fileType=PLUGIN'),
                       ('DownloadTag', 'generateTraceId'),
                       ('DownloadTag', 'PAG动画插件'),
                       ('DownloadTag', '二维码插件'),
                       ('DownloadTag', 'plugin'),
                       ('DownloadTag', '洗包监控'),
                       ('DownloadTag', 'startAllWaitingForMobileNetworkDownloadTask'),
                       ('DownloadTag', 'startAllWaitingForWifiDownloadTask'),
                       ('BookingPreDown_PullEngine', 'onRequestSuccessed res:')]
        filteredLogs = log_processor.delete_log_by_tags_and_content(filteredLogs, delete_logs)

        download_info = log_processor.format_log_to_download_info(filteredLogs)

        # app_logger.info(f"download_info = {download_info}")

        # app_logger.info(f'===== 二次过滤下载日志 ======\n{"".join(filteredLogs)}')

        # 请求大模型
        prompt = AUTO_DOWNLOAD_PROMPT.format(rag=rag, log_content="".join(filteredLogs),
                                        indexKey=";".join(indexKeys), user_action=self._user_action,
                                        query=self._query, download_info=download_info)
        yield from self._request_model(prompt)

    def _request_model(self, prompt):
        app_logger.info(prompt)

        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "Hunyuan-T1-32K"  # DeepSeek-R1
        # model = "DeepSeek-R1"
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        yield from self._stream_results(hunyuan_client.request(prompt))
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}


if __name__ == '__main__':
    logPath = '/Users/<USER>/Desktop/日志分析/auto_download/4/TDOSLog_20250109_113816955_14974_47784/com.tencent.android.qqdownloader_2025010911.xlog.log'
    startTime = None 
    endTime = None
    query = '请分析是否产生了自动下载？'
    autoDownloadAgent = AutoDownloadAgent(logPath, startTime, endTime, "", query)
    autoDownloadAgent.parse_auto_download_log()
