import os
import sys
from AiLogs.tools.log_process import LogProcessor
from AiLogs.rag.log_rag import log_rag
from AiLogs.logs.logger import app_logger
from AiLogs.client.hunyuan_client import HunyuanClient
from AiLogs.prompt.user_action_prompt import USER_ACTION_PROMPT
from AiLogs.prompt.user_action_prompt import LOG_PROMPT
from AiLogs.agent.download_agent import DownloadAgent
from AiLogs.agent.auto_download_agent import AutoDownloadAgent
from AiLogs.agent.other_issue_agent import OtherIssueAgent
from AiLogs.agent.install_agent import InstallAgent
from AiLogs.agent.cloud_game_agent import CloudGameAgent
from AiLogs.agent.video_agent import VideoAgent
from AiLogs.agent.kuikly_activity_agent import KuiklyActivityAgent
from AiLogs.agent.intent import UserIntent


# 过滤用户的行为日志
# 单独执行该文件：python3 -m agent.filter_user_action

class FilterUserAction():
    def __init__(self, logPath, startTime, endTime, bug_time, query, logs_path='', log_filenames=''):
        self._logPath = logPath
        self._start_time = startTime
        self._end_time = endTime
        self._bug_time = bug_time
        self._userActionTags = ['DownloadProxy',
                                'InstallStManager', 'event add:AppBeginInstall',
                                'loadKuiklyRenderView onComplete pageInfo', 'RuntimeCrash',
                                'BaseActivity'
                                ]
        self._indexKeys = {'scene'}
        self._logProcessor = None
        self._query = query
        self._error_action = ''
        self._log_filenames = log_filenames
        self._logs_path = logs_path

    # 分析用户行为
    # 1. 获取日志文件（输入文件地址）
    # 2. 根据用户指定的范围 过滤日志
    #   2.1 bug时间：2025-3-17 10:24:07 （提取 前后十分钟 的日志）
    #   2.2 用户行为相关 tag：UserActionLog
    #   2.3 用户行为 关键信息 的索引：scene
    # 3. 让大模型分析过滤出的用户行为日志
    def parse_user_action(self):
        # 过滤 用户行为 日志
        if not os.path.exists(self._logPath):
            print('日志文件地址不合法')
            raise ValueError(f"日志文件地址不合法: {self._logPath}")
        self._logProcessor = LogProcessor()
        self._logProcessor.process(self._logPath)
        filteredLogs = self._logProcessor.filter_log_by_tags_and_time(self._start_time,
                                                                      self._end_time,
                                                                      self._userActionTags)

        # 保存的关键日志，连续重复的日志，只保存第一条。
        save_logs = [('DownloadProxy', ''), 
                     ('BaseActivity', ''),
                     ('Kuikly-KRCommonActivity', 'loadKuiklyRenderView onComplete pageInfo'),
                     ('InstallRetryMgr', 'event add:AppBeginInstall'),
                     ('RuntimeCrash', '')]
        filteredLogs = self._logProcessor.save_log_by_tags_and_content(filteredLogs, save_logs)

        # 删除行为链的冗余日志行
        delete_logs = [('DownloadProxy', 'fileType=PLUGIN'), ('BaseActivity', '页面曝光'),
                       ('BaseActivity', 'passphrase'), ('BaseActivity', 'showFloatingBall'),
                       ('BaseActivity', 'onUserLeaveHint'),('BaseActivity', 'disMissKeyGuard')
                       ]
        filteredLogs = self._logProcessor.delete_log_by_tags_and_content(filteredLogs, delete_logs)

        matched_tags = self._logProcessor.get_matched_tags()
        app_logger.info("; ".join(matched_tags))

        # RAG 补充下载相关知识，构建索引
        # 根据 indexKey 找到 对应解释
        # 如 indexKey = 'scene'，scene = 10276， 那么 indexValues 为 scene:10276 表示 我的奖品
        indexValues = set()
        indexKeys = set()
        log_rag.prepareLogRag()
        for logLine in filteredLogs:
            for indexKey in self._indexKeys:
                key = self._logProcessor.findIndexKeyFromLog(indexKey, logLine)
                if key:
                    indexKeys.add(key)
                    key = key.replace(" ", "")
                    key = key.replace("=", ":")
                    indexValues.add(log_rag.queryExplainByCode(key))
        rag = ""
        for indexValue in indexValues:
            rag += f"{indexValue}\n"
        app_logger.info("".join(rag))
        # app_logger.info("".join(filteredLogs))


        # 处理 daemon
        # 传入tag，更换tag
        # self._userActionTags
        # if self._daemon_log_path:
        #     self._logProcessor.process(self._daemon_log_path)
        #     daemon_filteredLogs = self._logProcessor.filter_log_by_tags_and_time(self._start_time,
        #                                                               self._end_time,
        #                                                               self._userActionTags)

        # 1. 请求大模型，分析用户操作路径
        app_logger.info('======= 开始 分析用户操作行为链 ...  =========')
        prompt = USER_ACTION_PROMPT.format(rag=rag, log_content="".join(filteredLogs),
                                           indexKey=";".join(indexKeys))
        user_action = ''
        # user_action = self._request_model(prompt)

        for result in self._request_model(prompt):
            if result['type'] == 'all_answer':
                user_action = result['data']
            elif result['type'] == 'answer' or result['type'] == 'thinking':
                yield {"data": result['data'], "type": 'reasoning_content'}

        app_logger.info('======= 结束 分析用户操作行为链  =========')

        # todo function call
        # function_call = FunctionCall()
        # function_call.function_call(self._query, user_action)

        # 2. 用意图识别代替function call, 识别问题可能出现在哪些场景，调用子agent
        intent = UserIntent()
        scene = intent.parseUserIntent(self._query) 
        app_logger.info(f'意图识别query得到场景 = {scene}')
        print("您的问题场景是... ")

        # 如果未识别到具体场景，用行为链识别可能的场景
        if scene == None or (len(scene) == 1 and scene[0]["issue_scene"] == '其他'):
            app_logger.info('行为链分析问题场景')
            app_logger.info(f'意图识别行为链得到场景 = {scene}')
            scene = intent.parseUserIntent(user_action)

        if scene:
            # 调用子agent 进行详细日志行分析
            for issues_scene in scene:
                print(issues_scene['issue_scene'])
                if issues_scene['issue_scene'] == '下载':
                    download_agent = DownloadAgent(self._logPath, self._bug_time, user_action,
                                                self._query, self._logs_path)
                    yield from self._agent_stream_results(download_agent.parse_download_log()) 
                elif issues_scene['issue_scene'] == '自动下载':
                    auto_download_agent = AutoDownloadAgent(self._logPath, self._start_time, self._end_time, user_action,
                                                self._query)
                    yield from self._agent_stream_results(auto_download_agent.parse_auto_download_log())
                elif issues_scene['issue_scene'] == '安装':
                    install_agent = InstallAgent(self._logPath, self._start_time, self._end_time, user_action,
                                                self._query)
                    yield from self._agent_stream_results(install_agent.parse_install_log())                            
                elif issues_scene['issue_scene'] == '云游戏':
                    cloud_game_agent = CloudGameAgent(self._logPath, self._start_time, self._end_time,
                                                    self._query)
                    yield from self._agent_stream_results(cloud_game_agent.parse_cloud_game_log())
                elif issues_scene['issue_scene'] == '视频播放':
                    video_agent = VideoAgent(self._logPath, self._start_time, self._end_time)
                    yield from self._agent_stream_results(video_agent.parse_video_log())
                elif issues_scene['issue_scene'] == '活动参与':
                    activity_agent = KuiklyActivityAgent(self._logPath, self._start_time, self._end_time, self._query, user_action, self._logs_path)
                    yield from self._agent_stream_results(activity_agent.parse_kuikly_activity_log())
                    # yield from self.set_stream_output(activity_agent.parse_kuikly_activity_log())
                    # return
                else:
                    # 分析其他可能的异常
                    other_agent = OtherIssueAgent(self._logPath, self._start_time, self._end_time, user_action,
                                                self._query)
                    yield from self._agent_stream_results(other_agent.parse_other_log())                           
        else:
            app_logger.info(f'意图识别query得到场景 3 = {scene}')
            # 分析其他可能的异常
            other_agent = OtherIssueAgent(self._logPath, self._start_time, self._end_time, user_action,
                                        self._query)
            yield from self._agent_stream_results(other_agent.parse_other_log())                           


        # 3. 大模型做总结输出
        app_logger.info('======= 开始 汇总 答案  ...  =========')
        finalPrompt = LOG_PROMPT.format(query=self._query, user_action=user_action,
                                        error_action=self._error_action)

        yield from self.set_stream_output(self._request_model(finalPrompt))
        
    def set_stream_output(self, items):
        if items is None:
            app_logger.info(f"流式结果为空（None）")
            raise ValueError(f"流式结果为空（None）")
        # 中间过程：type=reasoning_content
        # 回答内容（流式片段）: type=content_answer
        # 最终答案(全部)：type=result_answer
        for result in items:
            if result['type'] == 'thinking':
                yield {"data": result['data'], "type": 'reasoning_content'}
            elif result['type'] == 'answer':
                yield {"data": result['data'], "type": 'content_answer'}
            elif result['type'] == 'all_answer':
                yield {"data": result['data'], "type": 'result_answer'}
            else:
                yield {"data": result['data'], "type": 'unknown'}
    
    
    # 答案总结 model
    def _request_model(self, prompt):
        app_logger.info(prompt)
        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "Hunyuan-T1-32K"  # DeepSeek-R1
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        for result in hunyuan_client.request(prompt):
            yield {"data": result['data'], "type": result['type']}
    
    def _agent_stream_results(self, items):
        if items is None:
            app_logger.info(f"流式结果为空（None）")
            raise ValueError(f"流式结果为空（None）")
        for result in items:
            if result['type'] == 'all_answer':
                self._error_action = self._error_action + result['data'] +'\n'
            elif result['type'] == 'answer' or result['type'] == 'thinking':
                yield {"data": result['data'], "type": 'reasoning_content'}


if __name__ == '__main__':
    logPath = ''
    # startTime = '2025-02-16 15:23:07.000'
    # endTime = '2025-02-16 15:33:21.000'
    startTime = ''
    endTime = ''
    query = '访问活动页时，展示的会员激活态不对'
    filterUserAction = FilterUserAction(logPath, startTime, endTime, query)
    filterUserAction.parse_user_action()
