# 根据时间来匹配文件名获取日志文件
    def get_log_files(self, file_path):
        if not file_path:
            raise ValueError("日志文件路径为空")
            
        # 获取子文件夹下的所有文件
        file_in_path = os.listdir(file_path)
        
        if not file_in_path:
            raise ValueError(f"日志文件夹 {file_path} 为空，未找到任何日志文件")

        # 没有 bug_time 选取最新时间，选择最新日志文件
        print(f'time = {self._bug_time}')
        app_logger.info(f'time = {self._bug_time}')
        if not self._bug_time:
            print('no time')
            app_logger.info("no time")

            # 获取一个最新日期的日志文件
            # file = self.get_latest_timestamp_log(file_in_path)
            # 获取两个最新时间戳的日志文件
            file = self.get_latest_two_logs(file_in_path)
            log_path = self.get_final_filename(file, file_path, is_get_daemon_filename=False)

            daemon_log_path = ''
            if self._is_analyze_daemon:
                # 选中的daemon文件名 列表
                daemon_files = self.get_daemon_files(file, file_in_path)
                # 获取daemon进程 日志文件 路径
                daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)     
            
            # 返回：主进程日志路径、daemon进程日志路径、所有日志文件名、日志文件夹 路径
            return log_path, daemon_log_path, file_in_path, file_path

        if not self._user_input:
            # 非企微机器人场景，bug_time 格式化为 %Y-%m-%d %H:%M:%S.%f
            self._bug_time += '.000'
        
        # 判断是否 相邻整点 相差在10分钟内，得到相邻整点字符串
        near_clock_str = self.is_within_ten_minutes()
        
        # 解析原始时间字符串为datetime对象
        datetime_obj = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        # 转换为目标格式的字符串
        target_time_str = datetime_obj.strftime("%Y%m%d%H")
        app_logger.info(f"用户输入bug时间：{target_time_str}")
        
        # 有 bug_time 根据bug时间选取日志文件
        log_files = []
        # 用来获取daemon文件
        files_for_daemon = []
        for file in file_in_path:
            if '@' not in file and target_time_str in file:
                files_for_daemon.append(file)
                log_files.append(os.path.join(file_path, file))
            elif near_clock_str and '@' not in file and near_clock_str in file:
                files_for_daemon.append(file)
                log_files.append(os.path.join(file_path, file))
        app_logger.info(f"要合并的日志路径：{log_files}")

        log_path = None
        daemon_log_path = None
        # 如果只有一个日志文件，直接返回该文件路径
        if len(log_files) == 1:
            log_path = os.path.join(file_path, file)
        # 合并日志文件，获取合并后的日志文件路径
        elif len(log_files) > 1:
            log_path = self.merge_logs(log_files)
        
        if self._is_analyze_daemon:
            # 选中的daemon文件名 列表
            daemon_files = self.get_daemon_files(files_for_daemon, file_in_path)
            # 获取daemon进程 日志文件 路径
            daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)

        
        # 如果根据bug_time找不到匹配的日志文件，则返回最新日期的日志文件
        if not log_path:
            self._isFileFoundByBugTime = False
            app_logger.warning(f"根据用户输入的bug时间 {target_time_str}，未找到对应的日志文件，将返回最新日志文件")
            # 获取一个最新日期的日志文件
            # file = self.get_latest_timestamp_log(file_in_path)
            # 获取两个最新时间戳的日志文件
            file = self.get_latest_two_logs(file_in_path)
            # 获取 主进程 日志文件路径
            log_path = self.get_final_filename(file, file_path, is_get_daemon_filename=False)
            
            if self._is_analyze_daemon:
                # 选中的daemon文件名 列表
                daemon_files = self.get_daemon_files(file, file_in_path)
                # 获取daemon进程 日志文件 路径
                daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)
        else:
            self._isFileFoundByBugTime = True
        
        app_logger.info(f"找到的主进程日志文件路径：{log_path}")
        app_logger.info(f"找到的daemon日志文件路径：{daemon_log_path}")
        # 返回：主进程日志路径、daemon进程日志路径、所有日志文件名、日志文件夹 路径
        return log_path, daemon_log_path, file_in_path, file_path
    
    def get_final_filename(self, file, file_path, is_get_daemon_filename):
        if file:
            if len(file) > 1:
                # 合并两个文件
                file_to_merge = []
                file_to_merge.append(os.path.join(file_path, file[0]))
                file_to_merge.append(os.path.join(file_path, file[1]))
                return self.merge_logs(file_to_merge)
            else:
                # 获取一个最新日期文件的路径
                return os.path.join(file_path, file[0])
            app_logger.info(f"返回最新时间戳的日志文件：{log_path}")
        else:
            if is_get_daemon_filename:
                return ''
            else:
                error_msg = "未找到任何符合条件的日志文件"
                app_logger.error(error_msg)
                raise ValueError(error_msg)
    
    def get_daemon_files(self, file, file_in_path):
        # 选中的daemon文件名 列表
        app_logger.info(f'== get_daemon_files ==\n主进程日志文件： {file}')
        daemon_files = []
        for file_name in file:
            daemon_file = self.add_log_filename_with_daemon(file_name)
            if daemon_file in file_in_path:
                daemon_files.append(daemon_file)
        app_logger.info(f'== get_daemon_files ==\ndaemon日志文件： {daemon_files}')
        return daemon_files


    def add_log_filename_with_daemon(self, filename: str) -> str:
        """
        将形如 'com.tencent.android.qqdownloader_2025032615.xlog.log' 的文件名
        转换成 'com.tencent.android.qqdownloader@daemon_2025032615.xlog.log'。

        规则：
        - 找到第一个下划线，将其替换为 '@daemon_'。
        - 其余部分保持不变。

        :param filename: 原始文件名字符串
        :return: 转换后的文件名字符串
        """
        # 找到第一个下划线的位置
        idx = filename.find('_')
        if idx == -1:
            # 如果没有下划线，返回原字符串
            return filename

        # 在第一个下划线处插入 '@daemon'
        return filename[:idx] + '@daemon' + filename[idx:]
    
    # 根据时间判断是否在十分钟内，并返回相邻整点格式为：%Y%m%d%H
    def is_within_ten_minutes(self):
        # 将字符串转换为 datetime 对象
        current_time = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        
        # 获取当前时间的小时
        current_hour = current_time.hour
        
        # 计算上一个整点和下一个整点
        previous_hour = current_hour - 1 if current_hour > 0 else 23
        next_hour = current_hour + 1 if current_hour < 23 else 0
        
        # 创建上一个整点和下一个整点的 datetime 对象
        previous_time = current_time.replace(hour=previous_hour, minute=0, second=0, microsecond=0)
        next_time = current_time.replace(hour=next_hour, minute=0, second=0, microsecond=0)
        
        # 检查与上一个整点的间隔
        if (current_time - previous_time) < timedelta(minutes=10):
            return previous_time.strftime("%Y%m%d%H")
        
        # 检查与下一个整点的间隔
        if (next_time - current_time) < timedelta(minutes=10):
            return next_time.strftime("%Y%m%d%H")
        
        return None
    
    # 合并日志文件
    def merge_logs(self, log_files):
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        merged_log_path = os.path.join(self._unzip_folder, f'merged_log_{current_time}.log')
        with open(merged_log_path, 'wb') as merged_file:
            for log_file in log_files:
                app_logger.info(f'正在合并 {log_file}')
                with open(log_file, 'rb') as f:
                    merged_file.write(f.read())
                    merged_file.write(b'\n')  # 添加换行符以分隔日志
        app_logger.info(f"合并日志文件成功，保存到: {merged_log_path}")
        return merged_log_path
    
    # 给出logs（日志文件名的列表） 根据日志文件名称获取最新的两个日志文件。
    def get_latest_two_logs(self, logs):
        # todo 可选项：是否开启daemon搜索
        # 排除包含 '@' 的日志文件名
        filtered_logs = [log for log in logs if '@' not in log]
        app_logger.info(f'get_latest_two_logs ===== filtered_logs = {filtered_logs}')

        sort_logs_by_timestamp = self.sort_logs_by_timestamp(filtered_logs)

        app_logger.info(f'get_latest_two_logs ===== sort_logs_by_timestamp[:2] = {sort_logs_by_timestamp[:2]}')
        return sort_logs_by_timestamp[:2]
    
    # 根据时间戳排序日志文件
    def sort_logs_by_timestamp(self, logs):
        def extract_timestamp(log):
            # 使用正则表达式提取时间戳部分
            match = re.search(r'_(\d{10})(?:_\d+)?\.xlog\.log$', log)
            if match:
                return match.group(1)
            return ''

        # 使用sorted函数和自定义键进行排序
        sorted_logs = sorted(logs, key=extract_timestamp, reverse=True)
        return sorted_logs
    
    # 根据日志文件名称获取最新的日志文件
    # 获取时间最晚的（2025021615）且没有_（com.tencent.android.qqdownloader_2025021615_1.xlog.log）的尾缀的字符串。
    def get_latest_timestamp_log(self, logs):
        app_logger.info("get_latest_timestamp_log")
        timestamp_pattern = re.compile(r'_(\d{10})(?!_)')
        
        latest_timestamp = None
        latest_log = None
        
        for log in logs:
            # 排除包含 '@' 的日志文件名
            if '@' in log:
                continue

            match = timestamp_pattern.search(log)
            if match:
                timestamp = match.group(1)
                if latest_timestamp is None or timestamp > latest_timestamp:
                    latest_timestamp = timestamp
                    latest_log = log
        app_logger.info(f'get_latest_timestamp_log ===== latest_log = {latest_log}')
        return latest_log