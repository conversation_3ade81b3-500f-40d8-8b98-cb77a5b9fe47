def _parse_log(self, 
               log_type_name, 
               log_tags, 
               save_logs=None, 
               delete_logs=None, 
               split_info=None, 
               dedup_targets=None, 
               extract_fields=None, 
               prompt_template=None, 
               is_fuzzy_match_tag=False):
    app_logger.info(f'======= 开始 {log_type_name} 分析  =========')

    log_filter = LogFilter(
        logs_path=self._logs_path,
        log_tags=log_tags,
        bug_time=self._bug_time,
        index_keys=self._indexKeys,
        save_logs=save_logs,
        delete_logs=delete_logs,
        split_info=split_info,
        dedup_targets=dedup_targets,
        extract_fields=extract_fields,
        is_fuzzy_match_tag=is_fuzzy_match_tag
    )

    filteredLogs, rag, extracted_info = log_filter.get_filtered_log()

    # 根据不同的prompt模板，传入不同的参数
    prompt = prompt_template.format(
        rag=rag if rag else "",
        log_content="".join(filteredLogs),
        user_action=self._user_action,
        query=self._query,
        **extracted_info
    )
    yield from self._request_model(prompt)


def parse_download_log(self):
    save_logs = [('DownloadTag', ''), 
                 ('halley-downloader-SectionTransport', 'readData retCode:'),
                 ('halley-downloader-SectionTransport', 'Direct:true send req'),
                 ('halley-downloader-SectionTransport', 'Transport finish on retCode:')]

    delete_logs = [('DownloadTag', 'fileType=PLUGIN'),
                   ('DownloadTag', 'generateTraceId'),
                   ('DownloadTag', 'PAG动画插件'),
                   ('DownloadTag', '二维码插件'),
                   ('DownloadTag', 'plugin'),
                   ('DownloadTag', '洗包监控'),
                   ('DownloadTag', 'startAllWaitingForMobileNetworkDownloadTask'),
                   ('DownloadTag', 'startAllWaitingForWifiDownloadTask')]

    extract_fields = {
        "download_info": "format_log_to_download_info"
    }

    yield from self._parse_log(
        log_type_name="下载异常",
        log_tags=self._download_tags,
        save_logs=save_logs,
        delete_logs=delete_logs,
        extract_fields=extract_fields,
        prompt_template=DOWNLOAD_PROMPT
    )


def parse_install_log(self):
    delete_logs = [('InstallQueue', 'onProgressChanged'),
                   ('InstallSessionPackage', 'recordStepTime'),
                   ('InstallSessionPackage', 'updateSessionInfo'),
                   ('InstallerListenerActivity','Phantom start finish')]

    yield from self._parse_log(
        log_type_name="安装异常",
        log_tags=self._log_tags,
        delete_logs=delete_logs,
        prompt_template=INSTALL_PROMPT
    )


def parse_kuikly_activity_log(self):
    save_logs = [('ReceivingRewardViewModel', 'orderStatus=3'),
                 ('YybActCommonReceiveManager', 'doShowResult instance'),
                 ('PageReporter_beaconReport', 'reportActivityComponentClick'),
                 ('YybLotteryView',''),
                 ('YybLotteryViewModel',''),
                 ('RuntimeCrash','')]

    split_info = [('ReceivingRewardViewModel','img'),
                  ('YybActCommonReceiveManager','propertyData')]

    dedup_targets = [('ReceivingRewardViewModel', 'doQueryLotteryResult item')]

    extract_fields = {
        "lottery_item_info": "format_log_to_lottery_item_info",
        "obtain_present_info": "format_log_to_obtain_present_info",
        "click_info": "format_log_to_click_info"
    }

    yield from self._parse_log(
        log_type_name="活动场景异常",
        log_tags=self._log_tags,
        save_logs=save_logs,
        split_info=split_info,
        dedup_targets=dedup_targets,
        extract_fields=extract_fields,
        prompt_template=KUIKLY_ACTIVITY_PROMPT,
        is_fuzzy_match_tag=True
    )