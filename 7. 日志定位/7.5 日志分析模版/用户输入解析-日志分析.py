import re
import ast

def parse_sections(text, section_prefix="### "):
    """
    将文本按section_prefix分割成标题-内容字典。
    标题为section_prefix后面的第一行内容，内容为标题和下一个标题之间的文本（多行）。
    """
    pattern = re.compile(rf"^{re.escape(section_prefix)}(.+)$", re.MULTILINE)
    sections = {}
    matches = list(pattern.finditer(text))
    for i, match in enumerate(matches):
        title = match.group(1).strip()
        start = match.end()
        end = matches[i+1].start() if i+1 < len(matches) else len(text)
        content = text[start:end].strip('\n\r ')
        sections[title] = content
    return sections

def safe_eval_list(list_str):
    """
    尝试用ast.literal_eval安全解析字符串形式的列表，失败返回空列表。
    """
    try:
        return ast.literal_eval(list_str)
    except Exception:
        return []

# 假设text是你的完整文本
text = """（你的完整文本）"""

sections = parse_sections(text)

# 日志链接，取第一行
log_link = sections.get("日志链接", "")
log_link = log_link.splitlines()[0].strip() if log_link else None

# bug时间，取第一行
bug_time = sections.get("bug时间", "")
bug_time = bug_time.splitlines()[0].strip() if bug_time else None

# 过滤tag，取第一行并解析
filter_tag_str = sections.get("过滤tag", "")
filter_tag_line = filter_tag_str.splitlines()[0].strip() if filter_tag_str else ""
filter_tag = safe_eval_list(filter_tag_line)

# prompt，完整多行内容
prompt = sections.get("prompt", "").strip()

print("日志链接:", log_link)
print("bug时间:", bug_time)
print("过滤tag:", filter_tag)
print("prompt:", prompt)