# 流程

日志分析

您的分析场景是？发送已有的场景文件
输入示例
### 分析场景
未预设/场景号

- 有预设：是否改写prompt（返回最新的prompt展示）
  - 10 改写
    - 示例日志分析 输入（复杂）
  - 11 不改写
    - 示例日志分析 输入（简单）
- 无预设：请输入prompt
    - 示例日志分析 输入（复杂）


是否改写prompt
10,11



# 分析场景

### 日志链接（必填）
日志下载链接 
### bug时间（可选）
yyyy-mm-dd hh:mm:ss 
### 场景（必填）
需要分析的场景
### 过滤tag（有预设可忽略）
['x','xx','xxx']
### prompt（有预设可忽略）
你是一名Android日志分析专家，擅长逐行阅读日志，通过日志轨迹还原用户操作流程，精准分析日志。请理解[用户问题]，对提供的[用户日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
填写用户问题

# [知识库]
填写相关背景知识或规则，辅助分析。（可选）

# [分析流程]
填写分析步骤或思路，帮助提高分析准确性。（可选）

# [格式]
填写期望输出格式。（无固定格式可以不写）

# [格式说明]
填写对输出格式的详细解释。（无固定格式可以不写）






# 示例模版

日志分析输入格式如下：
日志链接：日志下载链接
bug时间：yyyy-mm-dd hh:mm:ss
问题：xxx
---------
举例：
日志链接：https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
bug时间：2025-02-16 15:23:07 
问题：用户反馈下载失败，定位一下原因


# 帮助文档
1. 日志分析 请输入：1
2. prompt存储 请输入：2
3. 使用评价 请输入：3


## 日志分析
日志分析输入格式如下：
### 日志链接（必填）
日志下载链接 
### bug时间（可选）
yyyy-mm-dd hh:mm:ss 
### 过滤tag（有预设可忽略）
['x','xx','xxx']
### prompt（必填）
你是一名Android日志分析专家，擅长逐行阅读日志，通过日志轨迹还原用户操作流程，精准分析日志。请理解[用户问题]，对提供的[用户日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
填写用户问题

# [用户日志]
{user_log} （填写 {user_log} ，直接复制不用更改）

# [知识库]
填写相关背景知识或规则，辅助分析。（没有可以不写）

# [分析流程]
填写分析步骤或思路，帮助提高分析准确性。（没有可以不写）

# [格式]
填写期望输出格式。（无固定格式可以不写）

# [格式说明]
填写对输出格式的详细解释。（无固定格式可以不写）

---------

输入示例：
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-02-16 15:23:07 
### 过滤tag
['YybLotteryViewModel', 'YybActCommonReceiveManager', 'PageReporter_beaconReport']
### 删除的日志行
[
('DownloadTag', 'fileType=PLUGIN'),
('DownloadTag', 'generateTraceId'),
('DownloadTag', 'PAG动画插件')
]
### prompt
你是一名Android日志分析专家，擅长逐行阅读日志，通过日志轨迹还原用户操作流程，精准分析日志。请理解[用户问题]，对提供的[用户日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
我的奖品发货失败

# [用户日志]
{user_log}

# [知识库]
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`

# [分析流程]
1. 逐行扫描[用户日志]，定位包含关键字“actBtnConfigPropertyActBtnConfig”的日志行，从中提取“orderStatus”字段值。  
2. 根据orderStatus判断发货状态：  
   - 若orderStatus=3，判定为发货失败，结合[知识库]输出相应提示。
3. 进一步分析同一订单中orderStatus的变化轨迹：  
   - 若存在orderStatus由1变更为3，视为正常发货失败流程。  
   - 若orderStatus为1且未变为3，视为发货成功。  
4. 分析过程中所有结论均基于日志和知识库，不得无依据推断。


# [格式]
# 核心总结
# 关键证据链
# 日志总结

# [格式说明]
1. 核心总结。填写 简明结论，包含核心问题判断和处理建议。
2. 关键证据链。填写 原文日志，从[游戏运营活动页日志]中寻找证据。列出关键原文日志，说明原因。注意需要按时间顺序填写。 
3. 日志总结。填写 日志总结，具体内容如下：
   - 核心问题
   - 证据支持
      * [证据点1]
      * [证据点2]
   - 处理建议





【易事厅】满意度回访-工单ID 1559916 您的工单已于 2025-04-23 12:11:10 申请结单，请您对本次的服务进行评价!
如有其它意见，请点击反馈或拒绝结单

请点击选择满意度:
5星    4星    3星    2星    1星

（5星代表非常满意，1星代表非常不满意，星级越高代表越满意！）




# prompt保存模版
场景号（字符串描述/id）
{字符串描述, }

用python设计一下，数据结构
每条数据的内容如下
唯一场景值，对应一些属性，如 tag、prompt、delete_content 等

log_download_link: str = '' # 日志下载链接，与 log_path 二选一
log_path: str = '' # 日志的路径， 与 log_download_link 二选一
query: str = ''   # 问题
log_save_path: str = ''  # 日志下载链接 下载日志保存路径
bug_time: str = '' # bug时间
log_tags: List[str] # 需要过滤的tag
prompt: str = '' # 提示词 prompt
index_keys: Dict # rag索引的key，如果有建立rag索引表，可以利用index_keys（如retcode）找到对应的含义
save_logs: List  # 需要保存的日志列表 （默认连续出现的相同内容日志行，只保存第一行）
delete_logs: List  # 需要删除的日志列表
split_info: List # 需要进行截取的日志行列表
dedup_targets: List # 需要进行去重的日志行列表
is_fuzzy_match_tag: bool = False # 保存日志时，是否开启模糊tag匹配




{
    scene_id: str # 唯一场景
    version: int  # 版本号，新增内容 版本号+1
    desc: str # 描述信息
    tag: List[str] # tag列表
    prompt: str # prompt
    ...
}
