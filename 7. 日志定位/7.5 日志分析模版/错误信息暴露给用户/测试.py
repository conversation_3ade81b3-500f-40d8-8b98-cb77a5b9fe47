import logging
from enum import Enum, unique

# --- 错误码和异常定义（简化版） ---
@unique
class ErrorCode(Enum):
    INVALID_AGE = 1001
    EMPTY_USERNAME = 1002

ERROR_MESSAGES = {
    ErrorCode.INVALID_AGE: "年龄必须是非负整数",
    ErrorCode.EMPTY_USERNAME: "用户名不能为空",
}

class DetailedValueError(ValueError):
    def __init__(self, code: ErrorCode, message=None, context=None):
        if message is None:
            message = ERROR_MESSAGES.get(code, "未知错误")
        super().__init__(message)
        self.code = code
        self.context = context or {}

    def __str__(self):
        base = super().__str__()
        return f"{base} (code={self.code.name}[{self.code.value}], context={self.context})"

def raise_value_error(code: ErrorCode, context=None):
    raise DetailedValueError(code, context=context)

# --- 业务代码 ---
class User:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def set_age(self, age):
        if not isinstance(age, int) or age < 0:
            raise_value_error(ErrorCode.INVALID_AGE, context={"param": "age", "value": age})
        self.age = age

    def greet(self):
        if not self.name:
            raise_value_error(ErrorCode.EMPTY_USERNAME, context={"param": "name", "value": self.name})
        return f"你好，{self.name}！"

# --- 日志配置 ---
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s %(levelname)s %(message)s',
    filename='app.log',  # 也可以输出到控制台，去掉filename参数即可
    filemode='a'
)
logger = logging.getLogger(__name__)

# --- 主程序 ---
if __name__ == "__main__":
    user = User("Alice", 30)

    try:
        user.set_age(-5)
    except DetailedValueError as e:
        # 记录详细错误日志
        logger.error(f"错误码: {e.code.name}[{e.code.value}], 错误信息: {e}, 上下文: {e.context}")

    try:
        user.name = ""
        print(user.greet())
    except DetailedValueError as e:
        logger.error(f"错误码: {e.code.name}[{e.code.value}], 错误信息: {e}, 上下文: {e.context}")