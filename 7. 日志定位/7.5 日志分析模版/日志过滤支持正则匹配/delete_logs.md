下面的代码增加正则匹配。如果正则编译失败，退化为简单字符串比较

# 下载日志的过滤
# 日志格式：年月日 时间 级别 tag log_content。如
# 2025-02-16 15:27:53.176 I DownloadTag initDownload cancel task:com.assistant.qrcode_64,pkg:com.assistant.qrcode,name:二维码插件,state:PAUSED
# 遍历日志文件的日志行，删除 指定tag 中包含指定内容的日志行。如
# tag： DownloadTag
# log_content包含： fileType=PLUGIN
# 删除 DownloadTag 中有包含 fileType=PLUGIN 的日志行
# 参数：filteredLogs 原始日志
# 参数：delete_logs 过滤规则：[(tag1, useless_content1),(tag2, useless_content2),(tag3, useless_content3)]
def delete_log_by_tags_and_content(self, filteredLogs, delete_logs):
    remaining_logs = []
    for log in filteredLogs:
        # 将日志解析为 "年月日 时间 级别 tag 内容"
        log_dict = self.parse_log_line(log)
        if not log_dict:
            continue
        log_tag = log_dict['tag']
        log_content = log_dict['content']

        match_found = False
        # match_found = any(log_tag == tag and useless_content in log_content for tag, useless_content in delete_logs)
        for tag, useless_content in delete_logs:
            if log_tag == tag and useless_content in log_content:
                match_found = True
                break
        if not match_found:
            remaining_logs.append(log)
    return remaining_logs





你这段代码想增加正则匹配能力，且如果正则编译失败退化为简单字符串比较，我帮你改成如下版本：

```python
import re

def delete_log_by_tags_and_content(self, filteredLogs, delete_logs):
    """
    根据标签和内容删除日志，支持正则匹配。
    如果正则编译失败，退化为简单字符串比较。

    :param filteredLogs: 日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
    :param delete_logs: 过滤规则列表 [(tag_pattern, content_pattern), ...]
    :return: 删除匹配日志后的剩余日志列表
    """
    remaining_logs = []

    # 预编译正则，失败则为 None，后续用字符串匹配
    compiled_delete_logs = []
    for tag_pattern, content_pattern in delete_logs:
        try:
            tag_re = re.compile(tag_pattern)
        except re.error:
            tag_re = None
        try:
            content_re = re.compile(content_pattern)
        except re.error:
            content_re = None
        compiled_delete_logs.append((tag_re, content_re, tag_pattern, content_pattern))

    for log in filteredLogs:
        log_dict = self.parse_log_line(log)
        if not log_dict:
            continue
        log_tag = log_dict['tag']
        log_content = log_dict['content']

        match_found = False
        for tag_re, content_re, tag_pattern, content_pattern in compiled_delete_logs:
            # 标签匹配，正则匹配用 fullmatch，失败用字符串等于
            if tag_re:
                tag_match = bool(tag_re.fullmatch(log_tag))
            else:
                tag_match = (log_tag == tag_pattern)

            # 内容匹配，正则匹配用 search，失败用字符串包含
            if content_re:
                content_match = bool(content_re.search(log_content))
            else:
                content_match = (content_pattern in log_content)

            if tag_match and content_match:
                match_found = True
                break

        if not match_found:
            remaining_logs.append(log)

    return remaining_logs
```

---

### 说明
- `delete_logs` 中的 `(tag_pattern, content_pattern)` 支持正则表达式。
- 标签匹配用 `re.fullmatch`，内容匹配用 `re.search`。
- 如果正则编译失败，则退化为普通字符串匹配（标签用等于，内容用包含）。
- 这样既支持正则，也兼容普通字符串。

你可以根据需要调整匹配方式，比如标签用 `re.search` 还是 `re.fullmatch`，内容匹配是否支持模糊等。






# 支持正则版

    def delete_log_by_tags_and_content(self, filteredLogs, delete_logs):
        """
        根据标签和内容删除日志，支持正则匹配。
        如果正则编译失败，退化为简单字符串比较。

        :param filteredLogs: 日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
        :param delete_logs: 过滤规则列表 [(tag_pattern, content_pattern), ...]
        :return: 删除匹配日志后的剩余日志列表
        如：
        tag： DownloadTag
        log_content包含： fileType=PLUGIN
        删除 DownloadTag 中有包含 fileType=PLUGIN 的日志行
        """
        remaining_logs = []

        # 预编译正则，失败则为 None，后续用字符串匹配
        compiled_delete_logs = []
        for tag_pattern, content_pattern in delete_logs:
            try:
                tag_re = re.compile(tag_pattern)
            except re.error:
                tag_re = None
            try:
                content_re = re.compile(content_pattern)
            except re.error:
                content_re = None
            compiled_delete_logs.append((tag_re, content_re, tag_pattern, content_pattern))

        for log in filteredLogs:
            log_dict = self.parse_log_line(log)
            if not log_dict:
                continue
            log_tag = log_dict['tag']
            log_content = log_dict['content']

            match_found = False
            for tag_re, content_re, tag_pattern, content_pattern in compiled_delete_logs:
                # 标签匹配，正则匹配用 fullmatch，失败用字符串等于
                if tag_re:
                    tag_match = bool(tag_re.fullmatch(log_tag))
                else:
                    tag_match = (log_tag == tag_pattern)

                # 内容匹配，正则匹配用 search，失败用字符串包含
                if content_re:
                    content_match = bool(content_re.search(log_content))
                else:
                    content_match = (content_pattern in log_content)

                if tag_match and content_match:
                    match_found = True
                    break

            if not match_found:
                remaining_logs.append(log)

        return remaining_logs