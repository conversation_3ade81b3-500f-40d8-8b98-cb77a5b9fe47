import re
from typing import List, Tuple, Optional

def normalize_content(content: str, ignore_fields: List[str]) -> str:
    """
    通用归一化函数，替换日志内容中忽略字段对应的动态值为占位符。
    支持多种字段格式，不局限于 key=value。

    :param content: 原始日志内容
    :param ignore_fields: 需要忽略的字段名列表
    :return: 归一化后的内容
    """
    for field in ignore_fields:
        # 1. 尝试替换 key=value 或 key:value 或 key=“value”等形式
        # 匹配形如 field=xxx, field:xxx, field="xxx", field='xxx'
        # 这里假设字段值不包含空格、逗号、分号、括号等分隔符
        patterns = [
            rf'({re.escape(field)})=("[^"]*"|\'[^\']*\'|[^\s,;()]+)',
            rf'({re.escape(field)}):("[^"]*"|\'[^\']*\'|[^\s,;()]+)',
        ]
        replaced = False
        for pat in patterns:
            new_content, count = re.subn(pat, r'\1=:', content)
            if count > 0:
                content = new_content
                replaced = True

        if replaced:
            continue

        # 2. 如果没匹配到上述格式，尝试模糊匹配替换包含字段名的词
        # 例如字段名是 ssl，替换所有包含 ssl 的连续非空白字符串为 ssl=:
        # 这里用 word boundary 边界，避免误替换
        fuzzy_pattern = rf'\b\S*{re.escape(field)}\S*\b'
        content = re.sub(fuzzy_pattern, f'{field}=:',
                         content, flags=re.IGNORECASE)

    return content


def save_log_by_tags_and_content(
        self,
        filtered_logs: List[str],
        save_logs: List[Tuple[str, str]],
        split_info: Optional[List[Tuple[str, str]]] = None,
        is_fuzzy_match_tag: bool = False,
        dedup_targets: Optional[List[Tuple[str, str]]] = None,
        ignore_fields: Optional[List[str]] = None  # 新增参数
    ) -> List[str]:
    """
    根据标签和内容筛选日志，支持指定 (tag, content) 组合的全局去重，其他只去除连续重复。
    支持 split_info 截断日志内容。
    标签和内容匹配支持正则表达式。

    :param filtered_logs: 过滤后的日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
    :param save_logs: 需要保存的标签和内容列表，格式为 [(tag_regex, content_regex), ...]
    :param split_info: [(split_tag_regex, split_key), ...]，用于截断日志内容
    :param is_fuzzy_match_tag: 是否模糊匹配标签（模糊匹配时用 re.search，否则用 re.fullmatch）
    :param dedup_targets: 需要全局去重的 (tag_regex, content_regex) 列表
    :param ignore_fields: 需要归一化忽略的字段名列表，支持任意格式
    :return: 过滤后的关键日志列表
    """
    save_logs = save_logs or []
    split_info = split_info or []
    dedup_targets = dedup_targets or []
    ignore_fields = ignore_fields or []

    def tag_match(log_tag: str, target_tag_pattern: str) -> bool:
        try:
            pattern = re.compile(target_tag_pattern)
        except re.error:
            if is_fuzzy_match_tag:
                return target_tag_pattern in log_tag
            else:
                return log_tag == target_tag_pattern

        if is_fuzzy_match_tag:
            return bool(pattern.search(log_tag))
        else:
            return bool(pattern.fullmatch(log_tag))

    def content_match(log_content: str, target_content_pattern: str) -> bool:
        try:
            pattern = re.compile(target_content_pattern)
        except re.error:
            return target_content_pattern in log_content
        return bool(pattern.search(log_content))

    def should_save(log_tag: str, log_content: str) -> Optional[Tuple[str, str]]:
        for tag_pattern, content_pattern in save_logs:
            if tag_match(log_tag, tag_pattern) and content_match(log_content, content_pattern):
                return tag_pattern, content_pattern
        return None

    def needs_dedup(log_tag: str, log_content: str) -> bool:
        for d_tag_pattern, d_content_pattern in dedup_targets:
            if tag_match(log_tag, d_tag_pattern) and content_match(log_content, d_content_pattern):
                return True
        return False

    key_logs = []
    last_log_content_map = {}
    dedup_content_map = {}

    for log in filtered_logs:
        log_dict = self.parse_log_line(log)
        if not log_dict:
            continue

        log_tag = log_dict['tag']
        log_content = log_dict['content']

        # 处理 split_info 截断
        for split_tag_pattern, split_key in split_info:
            if tag_match(log_tag, split_tag_pattern) and split_key in log_content:
                log_content = log_content.split(split_key, 1)[0]
                break

        matched = should_save(log_tag, log_content)
        if not matched:
            continue

        tag_pattern, _ = matched

        # 归一化内容
        normalized_content = normalize_content(log_content, ignore_fields)

        if needs_dedup(log_tag, log_content):
            dedup_content_map.setdefault(tag_pattern, set())
            if normalized_content in dedup_content_map[tag_pattern]:
                continue
            dedup_content_map[tag_pattern].add(normalized_content)
        else:
            if last_log_content_map.get(tag_pattern) == normalized_content:
                continue
            last_log_content_map[tag_pattern] = normalized_content

        # 重新拼接日志行（如果内容被截断）
        if log_content != log_dict['content']:
            log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"

        key_logs.append(log)

    return key_logs








import re
from typing import List

def normalize_content_any_format(content: str, ignore_fields: List[str]) -> str:
    """
    任何格式下的归一化：
    - 将日志内容按非字母数字分割成词
    - 对每个词，如果包含忽略字段关键词，则替换成 '字段名=:'
    - 其他词保持不变
    - 最后用原分隔符拼回字符串

    这样可以覆盖各种格式，且不会误替换太多内容。
    """
    if not ignore_fields:
        return content

    # 构造忽略字段小写集合，方便忽略大小写匹配
    ignore_fields_lower = [f.lower() for f in ignore_fields]

    # 用正则分割，保留分隔符
    tokens = re.split(r'(\W+)', content)  # \W+匹配非字母数字的连续字符，作为分隔符

    def replace_token(token: str) -> str:
        token_lower = token.lower()
        for field in ignore_fields_lower:
            if field in token_lower:
                # 替换成 field=:
                return f"{field}=:"
        return token

    replaced_tokens = [replace_token(t) if i % 2 == 0 else t for i, t in enumerate(tokens)]
    return ''.join(replaced_tokens)