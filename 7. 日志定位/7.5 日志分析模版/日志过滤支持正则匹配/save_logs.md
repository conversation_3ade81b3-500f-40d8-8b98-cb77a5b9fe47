    def save_log_by_tags_and_content(
            self,
            filtered_logs: List[str],
            save_logs: List[Tuple[str, str]],
            split_info: Optional[List[Tuple[str, str]]] = None,
            is_fuzzy_match_tag: bool = False,
            dedup_targets: Optional[List[Tuple[str, str]]] = None
        ) -> List[str]:
        """
        根据标签和内容筛选日志，支持指定 (tag, content) 组合的全局去重，其他只去除连续重复。
        支持 split_info 截断日志内容。
        标签和内容匹配支持正则表达式。

        :param filtered_logs: 过滤后的日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
        :param save_logs: 需要保存的标签和内容列表，格式为 [(tag_regex, content_regex), ...]
        :param split_info: [(split_tag_regex, split_key), ...]，用于截断日志内容
        :param is_fuzzy_match_tag: 是否模糊匹配标签（模糊匹配时用 re.search，否则用 re.fullmatch）
        :param dedup_targets: 需要全局去重的 (tag_regex, content_regex) 列表
        :return: 过滤后的关键日志列表
        """
        save_logs = save_logs or []
        split_info = split_info or []
        dedup_targets = dedup_targets or []

        def tag_match(log_tag: str, target_tag_pattern: str) -> bool:
            try:
                pattern = re.compile(target_tag_pattern)
            except re.error:
                # 如果正则编译失败，退化为简单字符串比较
                if is_fuzzy_match_tag:
                    return target_tag_pattern in log_tag
                else:
                    return log_tag == target_tag_pattern

            if is_fuzzy_match_tag:
                return bool(pattern.search(log_tag))
            else:
                return bool(pattern.fullmatch(log_tag))

        def content_match(log_content: str, target_content_pattern: str) -> bool:
            try:
                pattern = re.compile(target_content_pattern)
            except re.error:
                # 如果正则编译失败，退化为简单字符串包含
                return target_content_pattern in log_content
            return bool(pattern.search(log_content))

        def should_save(log_tag: str, log_content: str) -> Optional[Tuple[str, str]]:
            """判断日志是否符合保存条件，返回匹配的 (tag_pattern, content_pattern) 或 None"""
            for tag_pattern, content_pattern in save_logs:
                if tag_match(log_tag, tag_pattern) and content_match(log_content, content_pattern):
                    return tag_pattern, content_pattern
            return None

        def needs_dedup(log_tag: str, log_content: str) -> bool:
            """判断当前日志是否需要全局去重"""
            for d_tag_pattern, d_content_pattern in dedup_targets:
                if tag_match(log_tag, d_tag_pattern) and content_match(log_content, d_content_pattern):
                    return True
            return False

        key_logs = []
        last_log_content_map = {}  # tag_pattern -> last content (连续去重)
        dedup_content_map = {}     # tag_pattern -> set of contents (全局去重)

        for log in filtered_logs:
            log_dict = self.parse_log_line(log)
            if not log_dict:
                continue

            log_tag = log_dict['tag']
            log_content = log_dict['content']

            # 处理 split_info 截断
            for split_tag_pattern, split_key in split_info:
                if tag_match(log_tag, split_tag_pattern) and split_key in log_content:
                    log_content = log_content.split(split_key, 1)[0]
                    break

            matched = should_save(log_tag, log_content)
            if not matched:
                continue

            tag_pattern, _ = matched
            if needs_dedup(log_tag, log_content):
                dedup_content_map.setdefault(tag_pattern, set())
                if log_content in dedup_content_map[tag_pattern]:
                    continue
                dedup_content_map[tag_pattern].add(log_content)
            else:
                if last_log_content_map.get(tag_pattern) == log_content:
                    continue
                last_log_content_map[tag_pattern] = log_content

            # 重新拼接日志行（如果内容被截断）
            if log_content != log_dict['content']:
                log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"

            key_logs.append(log)

        return key_logs



# 加入归一化去重 第一版

    def save_log_by_tags_and_content(
            self,
            filtered_logs: List[str],
            save_logs: List[Tuple[str, str]],
            split_info: Optional[List[Tuple[str, str]]] = None,
            is_fuzzy_match_tag: bool = False,
            dedup_targets: Optional[List[Tuple[str, str]]] = None,
            tag_ignore_patterns: Optional[Dict[str, List[str]]] = None,
            placeholder: str = ""
        ) -> List[str]:
        """
        根据标签和内容筛选日志，支持指定 (tag, content) 组合的全局去重，其他只去除连续重复。
        支持 split_info 截断日志内容。
        标签和内容匹配支持正则表达式。
        支持根据tag对应的忽略正则表达式对内容归一化后去重。

        :param filtered_logs: 过滤后的日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
        :param save_logs: 需要保存的标签和内容列表，格式为 [(tag_regex, content_regex), ...]
        :param split_info: [(split_tag_regex, split_key), ...]，用于截断日志内容
        :param is_fuzzy_match_tag: 是否模糊匹配标签（模糊匹配时用 re.search，否则用 re.fullmatch）
        :param dedup_targets: 需要全局去重的 (tag_regex, content_regex) 列表
        :param tag_ignore_patterns: dict，key是tag字符串，value是对应的忽略正则表达式列表
        :param placeholder: 替换匹配内容的占位符，默认"<IGNORE>"
        :return: 过滤后的关键日志列表
        """
        save_logs = save_logs or []
        split_info = split_info or []
        dedup_targets = dedup_targets or []
        tag_ignore_patterns = tag_ignore_patterns or {}

        # 预编译忽略正则表达式
        compiled_ignore_patterns = {
            tag: [re.compile(p) for p in patterns]
            for tag, patterns in tag_ignore_patterns.items()
        }

        def tag_match(log_tag: str, target_tag_pattern: str) -> bool:
            try:
                pattern = re.compile(target_tag_pattern)
            except re.error:
                if is_fuzzy_match_tag:
                    return target_tag_pattern in log_tag
                else:
                    return log_tag == target_tag_pattern

            if is_fuzzy_match_tag:
                return bool(pattern.search(log_tag))
            else:
                return bool(pattern.fullmatch(log_tag))

        def content_match(log_content: str, target_content_pattern: str) -> bool:
            try:
                pattern = re.compile(target_content_pattern)
            except re.error:
                return target_content_pattern in log_content
            return bool(pattern.search(log_content))

        def should_save(log_tag: str, log_content: str) -> Optional[Tuple[str, str]]:
            for tag_pattern, content_pattern in save_logs:
                if tag_match(log_tag, tag_pattern) and content_match(log_content, content_pattern):
                    return tag_pattern, content_pattern
            return None

        def needs_dedup(log_tag: str, log_content: str) -> bool:
            for d_tag_pattern, d_content_pattern in dedup_targets:
                if tag_match(log_tag, d_tag_pattern) and content_match(log_content, d_content_pattern):
                    return True
            return False

        def normalize_content(tag: str, content: str) -> str:
            patterns = compiled_ignore_patterns.get(tag, [])
            normalized = content
            for p in patterns:
                normalized = p.sub(placeholder, normalized)
            return normalized

        key_logs = []
        last_log_content_map = {}  # tag_pattern -> last normalized content (连续去重)
        dedup_content_map = {}     # tag_pattern -> set of normalized contents (全局去重)

        for log in filtered_logs:
            log_dict = self.parse_log_line(log)
            if not log_dict:
                continue

            log_tag = log_dict['tag']
            log_content = log_dict['content']

            # 处理 split_info 截断
            for split_tag_pattern, split_key in split_info:
                if tag_match(log_tag, split_tag_pattern) and split_key in log_content:
                    log_content = log_content.split(split_key, 1)[0]
                    break

            matched = should_save(log_tag, log_content)
            if not matched:
                continue

            tag_pattern, _ = matched

            # 归一化内容
            normalized_content = normalize_content(log_tag, log_content)

            if needs_dedup(log_tag, log_content):
                dedup_content_map.setdefault(tag_pattern, set())
                if normalized_content in dedup_content_map[tag_pattern]:
                    continue
                dedup_content_map[tag_pattern].add(normalized_content)
            else:
                if last_log_content_map.get(tag_pattern) == normalized_content:
                    continue
                last_log_content_map[tag_pattern] = normalized_content

            # 重新拼接日志行（如果内容被截断）
            if log_content != log_dict['content']:
                log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"

            key_logs.append(log)

        return key_logs
