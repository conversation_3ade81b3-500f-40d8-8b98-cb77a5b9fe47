好的，下面给你一个线程安全的全局单例 `SceneManager` 实现示例。

---

## 方案说明

- 使用类变量和类方法实现单例模式。  
- 使用 `threading.Lock` 保证多线程环境下单例实例创建安全。  
- `SceneManager` 依然是之前的功能，只是实例由类方法 `get_instance()` 获取。  
- 你调用 `SceneManager.get_instance()` 获取全局唯一实例。

---

## 代码示例

```python
import threading
import json
from dataclasses import dataclass, asdict, field
from typing import Optional, List, Dict, Any


@dataclass
class SceneData:
    scene_id: str
    version: int = 1
    tag: Optional[List[str]] = field(default_factory=list)
    prompt: Optional[str] = None
    delete_content: Optional[str] = None
    is_deleted: bool = False

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    def to_json(self, ensure_ascii=False) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=ensure_ascii)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SceneData":
        return cls(**data)

    @classmethod
    def from_json(cls, json_str: str) -> "SceneData":
        data = json.loads(json_str)
        return cls.from_dict(data)

    def update(self, tag: Optional[List[str]] = None, prompt: Optional[str] = None,
               delete_content: Optional[str] = None, is_deleted: Optional[bool] = None) -> "SceneData":
        new_version = self.version + 1
        return SceneData(
            scene_id=self.scene_id,
            version=new_version,
            tag=tag if tag is not None else self.tag,
            prompt=prompt if prompt is not None else self.prompt,
            delete_content=delete_content if delete_content is not None else self.delete_content,
            is_deleted=is_deleted if is_deleted is not None else self.is_deleted,
        )

    def validate(self) -> bool:
        if not self.scene_id:
            raise ValueError("scene_id不能为空")
        if self.version < 1:
            raise ValueError("version必须>=1")
        return True


class SceneManager:
    _instance = None
    _instance_lock = threading.Lock()

    def __init__(self):
        # 防止外部直接调用构造函数多次创建实例
        if SceneManager._instance is not None:
            raise RuntimeError("请使用 get_instance() 方法获取单例实例")
        self._data: Dict[str, List[SceneData]] = {}
        self._lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> "SceneManager":
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = cls.__new__(cls)
                    # 调用 __init__ 初始化实例变量
                    cls._instance.__init__()
        return cls._instance

    def add_scene(self, scene: SceneData):
        scene.validate()
        with self._lock:
            versions = self._data.setdefault(scene.scene_id, [])
            if any(s.version == scene.version for s in versions):
                raise ValueError(f"scene_id={scene.scene_id} 的版本 {scene.version} 已存在")
            versions.append(scene)
            versions.sort(key=lambda s: s.version)

    def get_latest(self, scene_id: str) -> Optional[SceneData]:
        with self._lock:
            versions = self._data.get(scene_id)
            if not versions:
                return None
            return versions[-1]

    def get_latest_active(self, scene_id: str) -> Optional[SceneData]:
        latest = self.get_latest(scene_id)
        if latest and not latest.is_deleted:
            return latest
        return None

    def get_all_versions(self, scene_id: str) -> List[SceneData]:
        with self._lock:
            return list(self._data.get(scene_id, []))

    def get_all_active_scenes(self) -> List[SceneData]:
        with self._lock:
            active_scenes = []
            for scene_id in self._data:
                scene = self.get_latest_active(scene_id)
                if scene:
                    active_scenes.append(scene)
            return active_scenes

    def update_scene(self, scene_id: str, tag: Optional[List[str]] = None,
                     prompt: Optional[str] = None, delete_content: Optional[str] = None) -> SceneData:
        with self._lock:
            latest = self.get_latest(scene_id)
            if latest is None:
                new_scene = SceneData(scene_id=scene_id, version=1, tag=tag, prompt=prompt, delete_content=delete_content)
            else:
                if latest.is_deleted:
                    raise ValueError(f"scene_id={scene_id} 已被删除，无法更新")
                new_scene = latest.update(tag=tag, prompt=prompt, delete_content=delete_content)
            self.add_scene(new_scene)
            return new_scene

    def delete_scene(self, scene_id: str) -> SceneData:
        with self._lock:
            latest = self.get_latest(scene_id)
            if latest is None:
                raise ValueError(f"scene_id={scene_id} 不存在，无法删除")
            if latest.is_deleted:
                raise ValueError(f"scene_id={scene_id} 已经被删除")
            deleted_version = latest.update(is_deleted=True)
            self.add_scene(deleted_version)
            return deleted_version

    def get_version(self, scene_id: str, version: int) -> Optional[SceneData]:
        with self._lock:
            versions = self._data.get(scene_id)
            if not versions:
                return None
            for scene in versions:
                if scene.version == version:
                    return scene
            return None

    def to_file(self, filename: str):
        with self._lock:
            with open(filename, 'w', encoding='utf-8') as f:
                for versions in self._data.values():
                    for scene in versions:
                        f.write(scene.to_json(ensure_ascii=False) + '\n')

    def load_file(self, filename: str):
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                scene = SceneData.from_json(line)
                try:
                    self.add_scene(scene)
                except ValueError as e:
                    print(f"跳过重复版本数据: {e}")

    def __str__(self):
        with self._lock:
            lines = []
            for scene_id, versions in self._data.items():
                lines.append(f"scene_id={scene_id}:")
                for scene in versions:
                    lines.append(f"  {scene}")
            return "\n".join(lines)


# ====== 使用示例 ======

if __name__ == "__main__":
    manager = SceneManager.get_instance()

    s1 = SceneData(scene_id="scene_001", version=1, tag=["tag1"], prompt="提示v1")
    manager.add_scene(s1)

    s2 = manager.update_scene("scene_001", prompt="提示v2")

    print("所有数据:")
    print(manager)

    # 获取指定版本
    v1 = manager.get_version("scene_001", 1)
    print("\nscene_001 版本1:", v1)

    v2 = manager.get_version("scene_001", 2)
    print("scene_001 版本2:", v2)

    # 删除
    manager.delete_scene("scene_001")
    print("\n删除后所有数据:")
    print(manager)

    # 再次获取最新未删除版本（应该是 None）
    latest_active = manager.get_latest_active("scene_001")
    print("\nscene_001 最新未删除版本:", latest_active)
```

---

## 说明

- `SceneManager.get_instance()` 返回全局唯一实例。  
- 直接调用 `SceneManager()` 会抛异常，避免误用。  
- 内部用 `_lock` 保护数据结构，保证线程安全。  
- 你可以在程序任何地方调用 `SceneManager.get_instance()`，拿到同一个实例，操作共享数据。  

---

如果你需要，我可以帮你写一个更复杂的多进程版本或者带持久化的版本。需要告诉我！