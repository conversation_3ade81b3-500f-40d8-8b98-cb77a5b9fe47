    def parse_user_intent(self):
        """
        解析用户意图
        """
        # 查询配置文件，获取已有预设场景
        body_content = self._iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014598562")
        scenes = []
        if body_content and body_content.lstrip().startswith('```'):
            # 提取代码块内容，得到json配置
            json_data = TextParseUtils.extract_code_block(body_content)
            json_items = TextParseUtils.parse_json_lines(json_data)
            scenes = [item.get('scene', '') for item in json_items]

        if not scenes:
            raise ValueError('预设场景配置为空')
        # 增加其他场景
        scenes.append("其他")
        print(f'scenes = {scenes}')
        # 用户意图场景及对应的 prompt iwiki 链接
        user_query_scene = ''
        url = ''
        user_intent_prompt = self._iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014606396")
        if not (user_intent_prompt and user_intent_prompt.lstrip().startswith('```')):
            raise ValueError('未找到用户意图分析Prompt')

        user_intent_prompt = TextParseUtils.extract_code_block(user_intent_prompt)
        user_intent_prompt = user_intent_prompt.format(query=self._query, scenes=scenes)
        print(f"user_intent_prompt = {user_intent_prompt}")

        items = self._request_model_for_user_intent(user_intent_prompt)
        if items is None:
            app_logger.info("流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")

        for result in items:
            if result['type'] == 'all_answer':
                data = json.loads(result['data'])
                user_query_scene = data.get("issue_scene")
                if self._ticket_id:
                    append_to_file(self._ticket_id, f'### 识别到的场景是：\n{user_query_scene}')
                if user_query_scene and user_query_scene != "其他":
                    url = TextParseUtils.get_field_by_scene(json_items, user_query_scene, "iwiki_url")
                    append_to_file(self._ticket_id, f'### 识别到的场景 以及 对应的prompt文件链接：\n{user_query_scene} -> {url}')
                    return user_query_scene, url
                else:
                    raise ValueError('未预设场景，请找 lichenlin 增加日志分析场景')