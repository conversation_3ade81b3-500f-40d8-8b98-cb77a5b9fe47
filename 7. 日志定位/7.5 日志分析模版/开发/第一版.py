import json
from dataclasses import dataclass, asdict, field
from typing import Optional, List, Tuple, Dict, Any

from logs.logger import app_logger


# 全局单例实例
scene_manager = SceneManager()

@dataclass
class SceneData:
    scene: str
    version: int = 1
    desc: Optional[str] = None
    tag: Optional[List[str]] = field(default_factory=list)
    prompt: Optional[str] = None
    delete_log: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    is_deleted: bool = False  # 逻辑删除标记

    def to_dict(self) -> Dict[str, Any]:
        """转成字典，元组会自动转成列表，json序列化兼容"""
        return asdict(self)

    def to_json(self, ensure_ascii=False) -> str:
        """转成 JSON 字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=ensure_ascii)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SceneData":
        """
        从字典创建实例。
        反序列化时，delete_log 中的元组会变成列表，需转回元组。
        """
        dc = data.get("delete_log")
        if dc is not None and isinstance(dc, list):
            # 把列表内的列表转换成元组
            data["delete_log"] = [tuple(item) for item in dc]
        return cls(**data)

    @classmethod
    def from_json(cls, json_str: str) -> "SceneData":
        """从 JSON 字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)

    def update(self, tag: Optional[List[str]] = None, prompt: Optional[str] = None,
               delete_log: Optional[List[Tuple[str, str]]] = None, is_deleted: Optional[bool] = None) -> "SceneData":
        """
        生成一个新版本的 SceneData，版本号自动+1，内容更新
        返回新的 SceneData 实例，不修改当前实例
        """
        new_version = self.version + 1
        return SceneData(
            scene=self.scene,
            version=new_version,
            tag=tag if tag is not None else self.tag,
            prompt=prompt if prompt is not None else self.prompt,
            delete_log=delete_log if delete_log is not None else self.delete_log,
            is_deleted=is_deleted if is_deleted is not None else self.is_deleted,
        )

    def __str__(self):
        return (f"SceneData(scene={self.scene}, version={self.version}, "
                f"tag={self.tag}, prompt={self.prompt}, delete_log={self.delete_log}, "
                f"is_deleted={self.is_deleted})")

    def validate(self) -> bool:
        """
        简单验证，比如 scene 不为空，version >=1
        你可以根据需求扩展
        """
        if not self.scene:
            raise ValueError("scene不能为空")
        if self.version < 1:
            raise ValueError("version必须>=1")
        return True


class SceneManager:
    """
    管理多个 SceneData，支持版本管理和文件存储
    """

    def __init__(self):
        # key: scene, value: list of SceneData，按版本升序排列
        self._data: Dict[str, List[SceneData]] = {}

    def add_scene(self, scene: SceneData):
        """添加一条数据，自动按版本排序"""
        app_logger.info(f"添加新版本数据: {scene}")
        scene.validate()
        versions = self._data.setdefault(scene.scene, [])
        # 检查版本号是否重复
        if any(s.version == scene.version for s in versions):
            raise ValueError(f"scene={scene.scene} 的版本 {scene.version} 已存在")
        versions.append(scene)
        versions.sort(key=lambda s: s.version)

    def get_latest(self, scene: str) -> Optional[SceneData]:
        """获取指定 scene 的最新版本"""
        versions = self._data.get(scene)
        if not versions:
            return None
        return versions[-1]

    def get_latest_active(self, scene: str) -> Optional[SceneData]:
        """
        获取指定 scene 的最新未删除版本
        如果最新版本是删除版本，则返回 None
        """
        latest = self.get_latest(scene)
        if latest and not latest.is_deleted:
            return latest
        return None

    def get_version(self, scene: str, version: int) -> Optional[SceneData]:
        """
        获取指定 scene 的指定版本
        """
        versions = self._data.get(scene)
        if not versions:
            return None
        for scene in versions:
            if scene.version == version:
                return scene
        return None

    def get_all_versions(self, scene: str) -> List[SceneData]:
        """获取指定 scene 的所有版本，按版本升序"""
        return self._data.get(scene, [])

    def get_all_active_scenes(self) -> List[SceneData]:
        """
        获取所有未删除的最新版本 SceneData
        """
        active_scenes = []
        for scene in self._data:
            scene = self.get_latest_active(scene)
            if scene:
                active_scenes.append(scene)
        return active_scenes

    def update_scene(self, scene: str, tag: Optional[List[str]] = None,
                     prompt: Optional[str] = None, delete_log: Optional[List[Tuple[str, str]]] = None) -> SceneData:
        """
        基于最新版本更新，生成新版本并添加
        返回新版本 SceneData
        """
        latest = self.get_latest(scene)
        if latest is None:
            # 如果不存在，创建版本1
            new_scene = SceneData(scene=scene, version=1, tag=tag, prompt=prompt, delete_log=delete_log)
        else:
            if latest.is_deleted:
                raise ValueError(f"scene={scene} 已被删除，无法更新")
            new_scene = latest.update(tag=tag, prompt=prompt, delete_log=delete_log)
        self.add_scene(new_scene)
        return new_scene

    def delete_scene(self, scene: str) -> SceneData:
        """
        逻辑删除某个 scene，新增一个版本，is_deleted=True
        返回删除版本的 SceneData
        """
        latest = self.get_latest(scene)
        if latest is None:
            raise ValueError(f"scene={scene} 不存在，无法删除")
        if latest.is_deleted:
            raise ValueError(f"scene={scene} 已经被删除")
        deleted_version = latest.update(is_deleted=True)
        self.add_scene(deleted_version)
        return deleted_version

    def to_file(self, filename: str):
        """保存所有数据到文件，每条一行 JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            for versions in self._data.values():
                for scene in versions:
                    f.write(scene.to_json(ensure_ascii=False) + '\n')

    def load_file(self, filename: str):
        """从文件加载数据，追加到当前管理器"""
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                scene = SceneData.from_json(line)
                try:
                    self.add_scene(scene)
                except ValueError as e:
                    print(f"跳过重复版本数据: {e}")

    def __str__(self):
        lines = []
        for scene, versions in self._data.items():
            lines.append(f"scene={scene}:")
            for scene in versions:
                lines.append(f"  {scene}")
        return "\n".join(lines)

@dataclass
class SceneData:
    scene: str
    desc: Optional[str] = None
    tag: Optional[List[str]] = field(default_factory=list)
    prompt: Optional[str]
    index_keys: Dict = field(default_factory=dict)
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    is_fuzzy_match_tag: bool = False
    is_deleted: bool = False  # 逻辑删除标记




if __name__ == "__main__":
    manager = scene_manager

    # 示例 delete_log
    delete_log = [
        ('DownloadTag', 'fileType=PLUGIN'),
        ('DownloadTag', 'generateTraceId'),
        ('DownloadTag', 'PAG动画插件'),
        ('DownloadTag', '二维码插件'),
        ('DownloadTag', 'plugin'),
        ('DownloadTag', '洗包监控'),
        ('DownloadTag', 'startAllWaitingForMobileNetworkDownloadTask'),
        ('DownloadTag', 'startAllWaitingForWifiDownloadTask')
    ]

    prompt = """你是一名Android日志分析专家，擅长逐行阅读日志，通过日志轨迹还原用户操作流程，精准分析日志。请理解[用户问题]，对提供的[用户日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
我的奖品发货失败

# [知识库]
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`

# [分析流程]
1. 逐行扫描[用户日志]，定位包含关键字“actBtnConfigPropertyActBtnConfig”的日志行，从中提取“orderStatus”字段值。  
2. 根据orderStatus判断发货状态：  
   - 若orderStatus=3，判定为发货失败，结合[知识库]输出相应提示。
3. 进一步分析同一订单中orderStatus的变化轨迹：  
   - 若存在orderStatus由1变更为3，视为正常发货失败流程。  
   - 若orderStatus为1且未变为3，视为发货成功。  
4. 分析过程中所有结论均基于日志和知识库，不得无依据推断。
"""
    # 新增版本1
    s1 = SceneData(scene="scene_001", version=1, tag=["tag1"], prompt=prompt, delete_log=delete_log)
    manager.add_scene(s1)

    # 基于最新版本更新，生成版本2
    s2 = manager.update_scene("scene_001", tag=["tag1", "tag2"], prompt="提示v2")

    # 新增另一条数据
    s3 = SceneData(scene="scene_002", version=1, tag=["tagA"], prompt="提示A")
    manager.add_scene(s3)

    print("所有数据:")
    print(manager)

    # 获取指定版本
    v1 = manager.get_version("scene_001", 1)
    print("\nscene_001 版本1:", v1)

    v2 = manager.get_version("scene_001", 2)
    print("scene_001 版本2:", v2)

    v3 = manager.get_version("scene_001", 3)
    print("scene_001 版本3:", v3)  # None，版本不存在

    # 删除 scene_001
    print("\n删除 scene_001 ...")
    manager.delete_scene("scene_001")

    print("\n删除后所有数据:")
    print(manager)

    print("\n获取 scene_001 最新未删除版本:")
    print(manager.get_latest_active("scene_001"))  # None，因为已删除

    print("\n获取所有未删除的最新版本:")
    for scene in manager.get_all_active_scenes():
        print(scene)

    # 保存到文件
    filename = "scenes_data.txt"
    manager.to_file(filename)

    # 新建管理器，加载文件
    new_manager = SceneManager()
    new_manager.load_file(filename)
    print("\n从文件加载的数据:")
    print(new_manager)
