专利交底书正文
1、【关键术语】本发明涉及到的关键术语和定义
下拉刷新：用于智能手机、ipad等智能终端。是指向下拉重新加载、刷新。
页面停留：是指用户在页面上停留，不进行上滑和下滑操作。
feed流：是指持续更新并呈现给用户内容的信息流。其中timeline是最典型的feed流展示方式，按照feed流内容更新的时间先后顺序将内容展示给用户，早期的微博、朋友圈都是典型的timeline 形式的feed流。
2、【发明构思】本发明涉及到的技术关键点或功能关键点介绍（必填）
目前随着社交产品的用户逐渐年轻化，用户喜好的社交方式发生了变化。越来越多的00后用户给自己贴上“声控”的标签现有方案沿用feed流的形式对不同用户的语音进行平铺直叙的展示，缺乏声音交友的互动趣味性和查看听过未声音的高效性。
本发明基于QQ扩列（一个针对00后群体的陌生人社交广场）的声音交友专区中，打造一个森林的场景，每个用户变身为一只小鸟以增强社交场景下的互动趣味性。在这个场景中，用户除了点击小鸟听声音，还可进行下拉刷新、以及页面停留来高效查看未听过的用户。从场景互动的趣味性和操作的高效性上促进用户添加好友，建立QQ关系链。
3、【背景技术】与本发明最相近的现有技术
3.1相关背景描述，以及现有技术的技术方案（必填）
目前Same App上有一个名为“声控福利社”的声音交友的专区。用户录制声音后，可选是否配文字，上传到一个Feed流中。Feed流的排序规则按照上传时间来倒序排列，下拉刷新可出最新的语音。用户点击声音可听声音，并且可在该声音下留言互动，实现用户间的根据声音来勾搭的诉求。
Same App上的声控福利社，用户上传了声音后就根据时间倒序，出现在Feed流里。（见图1）。用户在该页面上进行下拉刷新的操作后与一般feed流的设计一样，按时间顺序排列出最新的声音feed。停留无任何页面元素变化，上拉刷新即加载出之前的更多声音feed。

![Same APP上的声控福利社](image.png)
图1 Same APP上的声控福利社
3.2现有技术的缺点或尚未解决的问题，以及本专利技术方案可以解决的问题（必填）
A、现有技术的缺点或尚未解决的问题
目前Same App上的声控福利社，只是做纯粹声音的音波展示，没有为用户带来声音交友的氛围，较为工具化缺乏社交乐趣。并且在下拉刷新、停留以及底部上拉加载时只是根据时间倒序排列，无法分辨哪些声音听过，哪些没有听过，需要用户在该声音feed流页面里一直寻找没听过的声音。
B、专利技术方案可以解决的问题
    本发明基于QQ扩列（一个针对00后群体的陌生人社交广场）的声音社交场景，为用户营造了一个森林的情景，每个用户上传声音后都成为森林里的一只小鸟。用户可通过下拉刷新时，过滤掉已听过的声音，有效展现未听过的声音。在页面停留时，未听过声音的小鸟通过晃动身体来吸引用户点击听他的声音。这样的技术方案可在社交趣味性和操作便捷性上都得到提高，从而使用户获得良好的用户体验，促进声音交友。
4、【发明内容】本专利方案的详细阐述（图文并茂） 
4.1 产品侧（必填）
用户打开手机QQ，点击联系人tab的QQ扩列，切换到第三个tab妙音森林（见图2），进入一个声音社交专区，在这个动态页面中，用户可点击逐一查看妙音森林中不同人的声音。

![进入妙音森林（QQ扩列的声音社交专区）](image-1.png)
图2 进入妙音森林（QQ扩列的声音社交专区）
用户也可进入“我的”，在扩列资料里也上传了自己的声音，然后系统根据上传声音的时长来随机匹配给用户一个鸟的形象。例如上传2秒的声音就匹配一只小麻雀的形象，上传60秒的声音就匹配一只孔雀的形象。（见图3）


![上传声音匹配不同鸟的形象](image-2.png)
图3用户上传声音匹配不同鸟的形象
    当该用户进行下拉刷新的操作时，分为以下两种情况：
    第一种情况，刷新操作前，用户自己全都没有听过树上的鸟。上面新鸟飞到树顶，背景树的高度变高，整个树上的鸟变多，保证树上所有鸟都没被用户听过声音。
    第二种情况，刷新操作前，用户已听过部分树上的鸟。上面新鸟飞到树顶，已经被用户听过的鸟则飞到树底下的草坪上，保证树上所有的鸟都是没听过声音的鸟。背景树的高度根据鸟的多少决定高度，每只鸟以它自己为中心点扩散N px的密度大小（见图4）。

![下拉刷新对界面元素的影响](image-3.png)
图4下拉刷新对界面元素的影响
当用户在页面停留时间超过3秒无任何操作时，该页面可视区域的没被听过声音的鸟开始小幅度的晃动自己来吸引用户，用户点击小鸟则在当前页面上播放该用户的声音，可选择用户喜欢的声音加好友或者喜欢这个声音（见图5）。

![页面停留对界面元素的影响](image-4.png)
图5页面停留对界面元素的影响

4.2技术侧（必填）
这些鸟通过在新上传的语音库中以打分制的规则来出现在不同人的声音交友页面，同城加分、异性加分、主动加好友次数多、被加好友次数多的加分规则。技术方案主要是通过重绘背景视觉渲染层，以及控制页面上声音元素的位置来实现。该动态页面的基础分层模型见图6所示。

![动态页面的分层模型](image-5.png)
图6动态页面的分层模型
下拉刷新的第一种情况，在下拉刷新之前用户没有听过树上的鸟，下拉加载更多的鸟出现时，则需要更多的树来承载。这里树是有多个三角形组合而成。其中树的第2层至第n层是由一个750*550大小的三角形（这里我们命名为三角形X）复制而成，第1层是由70%大小的三角形X来构成。复制的规则为以三角形X为单位，向下平移三角形1/3的高度(见图7)。鸟的排列顺序则是按时间倒序的timeline形式排列。
     下拉刷新的第二种情况，在刷新前用户已听过部分树上的鸟。刷新后，鸟的排列顺序为新鸟——旧鸟（未听过）——旧鸟（已听过）。新鸟从顶部天空飞到树上，已经听过的旧鸟从树上飞到树底。始终保证已听过的鸟始终在树下，未听过的鸟按时间顺序倒序排列在树上。（见图7）

![下拉刷新时背景复制原则与鸟的排序原则](image-6.png)
图7下拉刷新时背景复制原则与鸟的排序原则
    当用户在页面停留时间超过3秒无任何操作时，该页面可视区域的没被听过声音的鸟开始以自己为中心的左右晃动自己的形象来吸引用户点击。（见图8）
具体实现过程见图9所示，用户作为终端发起方，在进入声音feed页后，用户可随意浏览点击自己感兴趣的声音。当用户在终端进行下拉刷新的操作时，后台则开始计算未听过声音的用户个数，根据个数多少来判断对背景三角形的复制/减少/维持不变，然后根据“新鸟——旧鸟（未听过）——旧鸟（已听过）”的排序规则对鸟（即用户）进行排序。传输数据到终端，重绘背景视觉渲染层以及调整声音元素的位置。接着在用户浏览声音时，在页面停留3秒以上无任何操作时，发送至指令到后台，识别当前页面元素中未听过声音的用户，以鸟形象为中心进行晃动，最终在终端上重绘声音元素层的动画。

![页面停留时鸟的晃动](image-7.png)
图8停留时鸟的晃动原则

![技术实现逻辑流程图](image-8.png)
图9 技术测实现逻辑流程图

4.3 技术方案所产生的有益效果（必填）
本发明的优势在于声音feed页面中，通过下拉刷新以及页面停留的操作，对页面元素进行影响，使用户在声音feed页面中，快速找到未听过声音的用户，从而减少用户在页面中对某一用户的反复收听声音。可提高用户认识新用户的效率，促进加好友，提升QQ关系链。
同时这种模拟森林里鸟类的声音交友，为用户在声音交友上增添了一定的乐趣，营造了一种场景化的交友氛围。
注意：技术效果的描述不能是凭空臆断出来的，最好能结合4.2技术侧内容中提出的具体解决方案，详细分析技术方案是如何解决相关技术问题，并实际达到相关技术效果的，可以辅助以实验数据或其他证据予以说明。

5、附件：参考文献（如：专利/论文/网页/期刊）
1、已公开中国发明专利，专利公开号为CN23***********A2；
2、已公开论文《基于******方法》，链接地址：http://************
